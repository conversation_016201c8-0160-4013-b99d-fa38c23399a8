﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class FriendUpdateNotify : IMessage
    {
        public const int Opcode = 243;

        public int Id => Opcode;
    }

    [Packable(Id = FriendUpdateNotify.Opcode)]
    public class FriendUpdateNotifyPackable : MessagePackable<FriendUpdateNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out FriendUpdateNotify message)
        {
            message = new FriendUpdateNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}