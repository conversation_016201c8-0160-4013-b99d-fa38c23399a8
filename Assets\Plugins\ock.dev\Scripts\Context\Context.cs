using System.Collections.Generic;
using System.Reflection;
using Autofac;
using Autofac.Annotation;

namespace Ock
{
    public class Context : IContext
    {
        public static readonly Dictionary<object, Context> Instances = new();

        public static Context Instance { get; private set; }

        public static Context GetInstance(object key = null)
        {
            if (key != null)
            {
                if (Instances.TryGetValue(key, out var context))
                    return context;
            }
            return Instance;
        }


        public object Key { get; }

        public IContainer Container { get; }

        public Context(object key, Assembly[] assemblies)
        {
            Key = key;
            Instance ??= this;
            Instances.Add(key, this);

            var builder = new ContainerBuilder();
            builder.RegisterModule(new AutofacAnnotationModule(assemblies));
            builder.RegisterInstance<IContext>(this);
            Container = builder.Build();
        }

        public T Resolve<T>() => Container.Resolve<T>();
    }
}