﻿using UnityEngine;

namespace CLMM
{
    [RequireComponent(typeof(Camera))]
    public class FollowCamera : MonoBehaviour
    {
        public Camera Camera { get; private set; }

        public Actor Target { private get; set; }

        public float SmoothTime = 0.125f;
        public Vector3 Offset = new(0, 0, -100);

        private Vector3 _velocity;

        private void Awake()
        {
            Camera = GetComponent<Camera>();
        }

        private void LateUpdate()
        {
            if (Target == null)
                return;

            this.transform.position = Vector3.SmoothDamp(
                this.transform.position,
                Target.Transform.position + Offset,
                ref _velocity,
                SmoothTime);
        }
    }
}