﻿using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEditor.U2D;
using UnityEngine;
using Object = UnityEngine.Object;

namespace CLMM.MirTools
{
    public partial class MirExport
    {
        [MenuItem("MitTools/Export All Actor SpriteAtlases")]
        private static void GenerateAllSpriteAtlases()
        {
            EditorUtility.DisplayProgressBar("Exporting All Actor SpriteAtlases", "Processing ...", 0);

            var mapping = new Dictionary<string, string>
            {
                { "Actor/Texture", "Actor/Atlas" }
            };

            try
            {
                foreach (var (key, value) in mapping)
                {
                    var guids = AssetDatabase.FindAssets("t:Texture", new[] { $"Assets/Content/{key}" });

                    var index = 0;
                    var count = guids.Length;
                    foreach (var guid in guids)
                    {
                        var texturePath = AssetDatabase.GUIDToAssetPath(guid);

                        EditorUtility.DisplayProgressBar(
                            $"Generating Actor SpriteAtlas({++index}/{count})",
                            "Processing " + texturePath,
                            (float)index / count);


                        var atlasPath = texturePath.Replace(key, value)
                            .Replace(Path.GetExtension(texturePath), ".spriteatlasv2");
                        var atlasAsset = SpriteAtlasAsset.Load(atlasPath);
                        if (atlasAsset != null)
                            continue;

                        atlasAsset = new SpriteAtlasAsset();
                        var folder = AssetDatabase.LoadAssetAtPath(texturePath, typeof(Object));
                        atlasAsset.Add(new[] { folder });

                        var dirPath = Path.GetDirectoryName(atlasPath);
                        if (!string.IsNullOrEmpty(dirPath) && !Directory.Exists(dirPath))
                            Directory.CreateDirectory(dirPath);

                        SpriteAtlasAsset.Save(atlasAsset, atlasPath);

                        return;
                    }
                }
            }
            finally
            {
                EditorUtility.ClearProgressBar();
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
                Resources.UnloadUnusedAssets();
            }
        }
    }
}