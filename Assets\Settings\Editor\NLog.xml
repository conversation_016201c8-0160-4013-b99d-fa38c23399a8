<?xml version="1.0" encoding="utf-8"?>

<nlog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xsi:schemaLocation="http://www.nlog-project.org/schemas/NLog.xsd NLog.xsd"
      autoReload="true"
      throwExceptions="true">

    <targets>
        <target xsi:type="File" name="File"
                fileName="${currentdir}/logs/${shortdate}.log"
                createDirs="true"
                layout="${longdate} ${threadid} ${uppercase:${level}} [${logger}] ${message}"/>

        <target xsi:type="File" name="SingleFile"
                fileName="${currentdir}/logs/last.log"
                createDirs="true" deleteOldFileOnStartup="true"
                layout="${longdate} ${threadid} ${uppercase:${level}} [${logger}] ${message}"/>

        <target xsi:type="UnityConsole" name="UnityConsole"
                layout="${date:format=fff} [${threadid}] ${logger} ${message}"/>
    </targets>

    <rules>
        <!--        <logger name="DotNetty.*" minlevel="Trace" final="true" writeTo="SingleFile,File"/>-->
        <!--        <logger name="Unity" minlevel="Trace" final="true" writeTo="SingleFile,File"/>-->
        <logger name="Universe.*" minlevel="Debug" writeTo="SingleFile,File,UnityConsole"/>
        <logger name="CLMM.Network.*" minlevel="Debug" writeTo="SingleFile,File,UnityConsole"/>
        <logger name="*" minlevel="Info" writeTo="SingleFile,File,UnityConsole"/>
    </rules>
</nlog>