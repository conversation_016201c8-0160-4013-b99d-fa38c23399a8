﻿using System;
using System.Text;
using DotNetty.Buffers;

namespace CLMM.Proto
{
    public struct SelCharInfo
    {
        public int Index;
        public string Name;
        public ushort Level;
        public byte Class;
        public byte Gender;
        public DateTime LastAccess;
    }

    public static class SelCharInfoSerializerExtensions
    {
        public static SelCharInfo ReadSelCharInfo(this IByteBuffer buf)
        {
            var info = new SelCharInfo();
            info.Index = buf.ReadIntLE();
            info.Name = buf.ReadFixedString(Encoding.UTF8);
            info.Level = buf.ReadUnsignedShortLE();
            info.Class = buf.ReadByte();
            info.Gender = buf.ReadByte();
            info.LastAccess = TimeZoneInfo.ConvertTimeFromUtc(
                DateTime.FromBinary(buf.ReadLongLE()), TimeZoneInfo.Local);

            return info;
        }
    }
}