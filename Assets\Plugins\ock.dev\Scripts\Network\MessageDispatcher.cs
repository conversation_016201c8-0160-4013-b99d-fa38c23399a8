using System;
using System.Collections.Generic;
using Autofac.Annotation;
using Newtonsoft.Json;
using slf4net;

namespace Ock
{
    [Component(typeof(IMessageDispatcher), AutofacScope = AutofacScope.InstancePerLifetimeScope)]
    public class MessageDispatcher : IMessageDispatcher
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(MessageDispatcher));


        private readonly Dictionary<int, List<IMessageHandler>> _handlerMappings = new();

        private readonly List<IMessageHandler> _handlerCache = new();

        public void Register(int id, IMessageHandler handler)
        {
            if (Logger.IsTraceEnabled)
                Logger.Trace($"{nameof(Register)} {id} {handler}");

            if (!_handlerMappings.TryGetValue(id, out var handlers))
            {
                handlers = new List<IMessageHandler>();
                _handlerMappings.Add(id, handlers);
            }

            handlers.Add(handler);
        }

        public void Unregister(int id, IMessageHandler handler)
        {
            if (Logger.IsTraceEnabled)
                Logger.Trace($"{nameof(Unregister)} {id} {handler}");

            if (!_handlerMappings.TryGetValue(id, out var handlers))
                return;

            handlers.Remove(handler);
        }

        public void Dispatch(ISession session, IMessage message)
        {
            if (!_handlerMappings.TryGetValue(message.Id, out var handlers))
                return;

            _handlerCache.AddRange(handlers);

            foreach (var handler in _handlerCache)
            {
                try
                {
                    handler.Handle(session, message);
                }
                catch (Exception e)
                {
                    if (Logger.IsErrorEnabled)
                        Logger.Error($"{nameof(Dispatch)}: -I {session} {JsonConvert.SerializeObject(message)}\r\nException - {e}");
                }
            }

            _handlerCache.Clear();
        }
    }
}