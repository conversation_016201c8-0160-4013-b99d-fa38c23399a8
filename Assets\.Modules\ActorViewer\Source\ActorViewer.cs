﻿using UnityEngine;
using UnityEngine.AddressableAssets;

namespace CLMM.MapViewer
{
    public class ActorViewer : MonoBehaviour
    {
        /*private SpriteAnimator _animator;

        private async void Start()
        {
            var initializeHandle = Addressables.InitializeAsync();
            await initializeHandle.Task;

            var handle = Addressables.LoadAssetAsync<GameObject>("Actor/Prefab/Model");
            var prefab = await handle.Task;
            var go = Instantiate(prefab);
            Addressables.Release(handle);
            go.name = "Model";

            _animator = new SpriteAnimator(go.GetComponent<SpriteRenderer>());

            var controllerHandle = Addressables.LoadAssetAsync<SpriteAnimatorController>("Actor/Animator/00/000.spriteanimatorcontroller");
            var controller = await controllerHandle.Task;
            _animator.Controller = controller;
            _animator.Play(9, 2);
        }

        private void Update()
        {
            if (_animator == null)
                return;

            _animator.Update();

            if (Input.GetKeyDown(KeyCode.Space))
            {
                if (!_animator.IsPlaying)
                    _animator.Play(9, Random.Range(0, 7));
            }
        }*/
    }
}