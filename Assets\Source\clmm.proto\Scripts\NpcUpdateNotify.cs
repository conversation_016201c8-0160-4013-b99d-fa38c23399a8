﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class NpcUpdateNotify : IMessage
    {
        public const int Opcode = 185;

        public int Id => Opcode;
    }

    [Packable(Id = NpcUpdateNotify.Opcode)]
    public class NpcUpdateNotifyPackable : MessagePackable<NpcUpdateNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out NpcUpdateNotify message)
        {
            message = new NpcUpdateNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}