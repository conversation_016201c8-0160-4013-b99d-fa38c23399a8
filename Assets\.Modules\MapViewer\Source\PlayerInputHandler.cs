using UnityEngine;
using UnityEngine.InputSystem;

namespace CLMM.MapViewer
{
    public class PlayerInputHandler : MonoBehaviour
    {
        private Camera _mainCamera;

        private InputAction _pointAction;
        private InputAction _pressAction;
        private bool _isPressing;
        private bool _isDragging;
        private Vector2 _dragStartPosition;
        private Vector3 _cameraStartPosition;

        public Actor Target { get; set; }

        private void Awake()
        {
            _mainCamera = Camera.main;

            var inputActions = new InputActionMap("DragControls");
            _pointAction = inputActions.AddAction("Point", binding: "<Mouse>/position");
            // _pointAction.AddBinding("<Touchscreen>/touch*/position");
            _pressAction = inputActions.AddAction("Drag", binding: "<Mouse>/leftButton");
            // _pressAction.AddBinding("<Touchscreen>/touch*/press");
            inputActions.Enable();

            _pressAction.started += OnStartPress;
            _pressAction.canceled += OnEndPress;
            _pointAction.performed += OnPerformPoint;
        }

        private void OnStartPress(InputAction.CallbackContext context)
        {
            _isPressing = true;
        }

        private void OnEndPress(InputAction.CallbackContext context)
        {
            if (_isDragging)
            {
                _isDragging = false;
                // OnEndDrag();
            }
            _isPressing = false;
        }

        private void OnPerformPoint(InputAction.CallbackContext context)
        {
            if (_isPressing)
            {
                if (_isDragging)
                {
                    OnDragging();
                }
                else
                {
                    _isDragging = true;
                    OnStartDrag();
                }
            }
        }

        private void OnStartDrag()
        {
            _dragStartPosition = _pointAction.ReadValue<Vector2>();
            _cameraStartPosition = _mainCamera.transform.position;
        }

        private void OnDragging()
        {
            var startPoint = _mainCamera.ScreenToWorldPoint(_dragStartPosition);
            var currentPosition = _pointAction.ReadValue<Vector2>();
            var currentPoint = _mainCamera.ScreenToWorldPoint(currentPosition);
            var delta = currentPoint - startPoint;
            delta.z = 0;
            var position = _cameraStartPosition - delta;
            _mainCamera.transform.position = position;
            Target.Location = new Vector2Int(
                (int)(position.x / World.TileSize.x),
                -(int)(position.y / World.TileSize.y));
        }

        // private void OnEndDrag() { }
    }
}