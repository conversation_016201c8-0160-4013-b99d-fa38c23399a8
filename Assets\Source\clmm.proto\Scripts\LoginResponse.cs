﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class LoginResponse : IMessage
    {
        public const int Opcode = 7;

        public int Id => Opcode;

        public byte Result;
    }

    [Packable(Id = LoginResponse.Opcode)]
    public class LoginResponsePackable : MessagePackable<LoginResponse>
    {
        protected override void Deserialize(IByteBuffer buf, out LoginResponse message)
        {
            message = new LoginResponse();
            message.Result = buf.ReadByte();
        }
    }
}