﻿using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEditor.U2D.Sprites;
using UnityEngine;

namespace CLMM.MirTools
{
    public partial class MirExport
    {

        [MenuItem("MitTools/Export All Actor Textures")]
        public static void ExportAllActorTextures()
        {
            EditorUtility.DisplayProgressBar("Exporting All Actor Textures", "Processing ...", 0);

            try
            {
                const string folder = "E:/Workspace/Project/Crystal/Build/Client/Debug/Data";

                var fileMapping = new Dictionary<string, (string, int)>
                {
                    // { "Monster", "Actor/Texture" }
                    { "CArmour", ("Equipment/CArmour/Texture", 1) }
                };

                foreach (var (key, (path, length)) in fileMapping)
                {
                    var files = Directory.GetFiles(Path.Combine(folder, key), "*.lib");
                    var index = 0;
                    var count = files.Length;
                    foreach (var file in files)
                    {
                        var filename = Path.GetFileNameWithoutExtension(file);
                        var output = $"Assets/Content/{path}/{filename[..length]}";

                        EditorUtility.DisplayProgressBar(
                            $"Exporting Actor Texture {index}/{count}",
                            filename,
                            (float)++index / count);

                        ExportActorTexture(file, output);

                        if (index > 1)
                            return;
                    }
                }
            }
            finally
            {
                EditorUtility.ClearProgressBar();
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
                Resources.UnloadUnusedAssets();
            }
        }

        private static void ExportActorTexture(string input, string output)
        {
            var filename = Path.GetFileNameWithoutExtension(input);
            output = Path.Combine(output, filename);

            var path = $"{output}.png";
            if (File.Exists(path))
                return;

            var textures = new List<Texture2D>();
            var ids = new List<int>();
            var rects = new List<Rect>();
            var pivots = new List<Vector2>();

            using var lib = new MirLibV2(input);
            var count = lib.Images.Length;
            for (var i = 0; i < count; i++)
            {
                var image = lib.LoadImage(i);
                if (image == null)
                    continue;
                if (image.Width <= 4 || image.Height <= 4 || image.Data == null)
                    continue;

                var texture = GenerateTexture2D(image.Width, image.Height, image.Data);
                textures.Add(texture);
                ids.Add(i);
                pivots.Add(new Vector2(
                    (float)-image.X / image.Width,
                    (-16f * 2 + image.Y + image.Height) / image.Height));
            }

            var mainTexture = new Texture2D(4096, 4096);
            var uvRects = mainTexture.PackTextures(textures.ToArray(), 4, 4096);

            if (CheckTextureSizeChanged(textures, uvRects, 4096))
            {
                Debug.LogError($"Texture size changed. {path}");
                return;
            }

            foreach (var uv in uvRects)
                rects.Add(new Rect(
                    uv.x * mainTexture.width,
                    uv.y * mainTexture.height,
                    uv.width * mainTexture.width,
                    uv.height * mainTexture.height));

            var directory = Path.GetDirectoryName(path);
            if (!Directory.Exists(directory))
                Directory.CreateDirectory(directory);

            File.WriteAllBytes(path, mainTexture.EncodeToPNG());

            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            Resources.UnloadUnusedAssets();

            GenerateActorSprite(path, ids, rects, pivots);
        }

        private static void GenerateActorSprite(string path,
            IReadOnlyList<int> ids,
            IReadOnlyList<Rect> rects,
            IReadOnlyList<Vector2> pivots)
        {
            var textureImporter = AssetImporter.GetAtPath(path) as TextureImporter;
            if (textureImporter == null)
                return;

            var factory = new SpriteDataProviderFactories();
            factory.Init();
            var provider = factory.GetSpriteEditorDataProviderFromObject(textureImporter);
            provider.InitSpriteEditorDataProvider();

            var spriteRects = new List<SpriteRect>();
            var nameFileIdPairs = new List<SpriteNameFileIdPair>();
            for (var i = 0; i < rects.Count; i++)
            {
                var spriteRect = new SpriteRect
                {
                    name = $"{ids[i]}",
                    spriteID = GUID.Generate(),
                    rect = rects[i],
                    alignment = SpriteAlignment.Custom,
                    pivot = pivots[i]
                };
                spriteRects.Add(spriteRect);
                nameFileIdPairs.Add(new SpriteNameFileIdPair(spriteRect.name, spriteRect.spriteID));
            }

            provider.SetSpriteRects(spriteRects.ToArray());

            var spriteNameFileIdDataProvider = provider.GetDataProvider<ISpriteNameFileIdDataProvider>();
            spriteNameFileIdDataProvider.SetNameFileIdPairs(nameFileIdPairs);

            provider.Apply();
            textureImporter.SaveAndReimport();
        }
    }
}