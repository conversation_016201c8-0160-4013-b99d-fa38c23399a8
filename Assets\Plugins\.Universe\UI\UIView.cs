﻿using Autofac;
using UnityEngine;
using ILogger = slf4net.ILogger;
using Object = UnityEngine.Object;

namespace Universe
{
    public sealed class UIView : UIElement, IUIView
    {
        private static readonly ILogger Logger = LoggerManager.GetCurrentClassLogger();

        private IResourceHandle<GameObject> _resourceHandle;
        private GameObject _gameObject;

        public UIView(ILifetimeScope container, string name)
            : this(container, name, null)
        {
        }

        public UIView(ILifetimeScope container, string name, object userdata)
            : base(name, userdata)
        {
            if (container.TryResolveNamed(_name, out _mediator))
                _mediator.UI = this;
            CreateAsync(container);
        }

        public async void CreateAsync(ILifetimeScope container)
        {
            var resourceManager = container.Resolve<IResourceManager>();
            _resourceHandle = resourceManager.LoadAssetAsync<GameObject>($"UI/Prefab/{_name}");
            var prefab = await _resourceHandle.Task;
            if (!_resourceHandle.IsValid())
            {
                if (Logger.IsErrorEnabled)
                    Logger.Error($"{nameof(CreateAsync)} ResourceHandle is invalid for {_name}");
                return;
            }

            if (prefab == null)
            {
                if (Logger.IsErrorEnabled)
                    Logger.Error($"{nameof(CreateAsync)} Prefab is null for {_name}");
                return;
            }

            _gameObject = Object.Instantiate(prefab);
            _gameObject.name = _name;
            _transform = _gameObject.transform;
            _mediator?.OnCreate();

            if (_parent != null)
            {
                _transform.SetParent(_parent.Transform, false);
                _mediator?.OnRegister();
            }
            else
                _transform.SetParent(_cacheTransform, false);

            StateSubject.OnNext(1);
        }

        public override void SetParent(IUIElement parent, Transform parentTransform = null)
        {
            if (_parent != null)
            {
                _parent.Children.Remove(this);

                if (!ReferenceEquals(_transform, null))
                {
                    _transform.SetParent(_cacheTransform, false);
                    _mediator?.OnUnregister();
                }
            }

            _parent = parent;

            if (_parent != null)
            {
                _parent.Children.Add(this);

                if (!ReferenceEquals(_transform, null))
                {
                    _transform.SetParent(!ReferenceEquals(parentTransform, null)
                        ? parentTransform
                        : _parent.Transform, false);
                    _mediator?.OnRegister();
                }
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (!disposing)
                return;

            base.Dispose(true);

            if (_gameObject != null)
            {
                Object.Destroy(_gameObject);
                _gameObject = null;
            }

            _resourceHandle?.Dispose();
        }
    }
}