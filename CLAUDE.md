# CLAUDE.md

使用中文回答

本文件为 Claude Code (claude.ai/code) 在处理此代码仓库时提供指导。

## 项目概述

Universe 是一个基于 Unity 的多人游戏项目，使用现代 Unity 技术重建经典 MMORPG。该项目使用 C# 和 Unity 2022.3，采用了多种关键架构模式和外部库。

## 构建命令

这是一个 Unity 项目 - 使用 Unity 编辑器来构建和运行：
- 在 Unity 编辑器中打开项目
- 通过 Unity 的构建设置进行构建 (File → Build Settings)
- 主游戏场景位于 `Assets/Scenes/Game.unity`

对于 Unity 中的 C# 项目：
- Unity 会在脚本更改时自动编译 C# 脚本
- 使用 Unity 的控制台窗口查看编译错误
- 程序集定义 (`.asmdef`) 控制编译单元

## 架构概述

### 核心框架 (`Assets/Plugins/Universe/`)
- **依赖注入**: 使用 Autofac 作为 IoC 容器，配合自定义 `ComponentAttribute` 进行服务注册
- **事件系统**: 自定义事件分发器用于解耦通信
- **网络层**: 基于 TCP 的网络通信，使用 DotNetty 和自定义协议序列化
- **UI 框架**: 自定义 UI 系统，使用中介者模式进行 View-ViewModel 分离
- **资源管理**: 集成 Addressable Asset System 进行动态加载
- **日志记录**: NLog 集成，输出到 Unity 控制台

### 游戏核心 (`Assets/Source/Game/`)
- **入口点**: `Program.cs` 初始化 DI 容器并启动 `GameBehavior`
- **游戏循环**: `GameBehavior` 单例管理场景、服务和数据包处理
- **场景管理**: 基于场景的架构，包含 `LoginScene`、`SelectScene`、`WorldScene`
- **实体系统**: 基于 Actor 的系统，使用 `SpriteAnimator` 进行 2D 精灵动画
- **世界系统**: 基于区块的世界，32x32 瓦片区块，2 层瓦片地图渲染
- **网络协议**: MemoryPack 序列化，配合自定义数据包处理器

### 模块 (`Assets/Modules/`)
- **ActorViewer**: 查看和编辑游戏角色的独立工具
- **MapViewer**: 查看和编辑游戏地图的独立工具
- **MirTools**: 用于纹理、精灵和地图数据的遗留游戏资产导入工具

### 关键组件

#### GameBehavior (主游戏控制器)
- 单例模式管理游戏生命周期
- 服务容器初始化和管理
- 场景切换逻辑
- 网络数据包注册
- 位于 `Assets/Source/Game/GameBehavior.cs:14`

#### 依赖注入设置
- 在 `Program.cs:17-23` 中注册的模块：LoggingModule、EventModule、ResourceModule、NetworkModule、UIModule、GameModule
- 服务通过 `ComponentAttribute` 注解自动注册
- 数据包创建使用工厂模式

#### 世界系统
- 在 `World.cs:6-21` 中的静态世界配置
- 基于区块的流式传输，`ChunkSize = 32`
- 双层瓦片地图渲染系统
- 世界坐标到 Unity 位置转换

#### 网络架构
- 使用 DotNetty 的 TCP 客户端
- 自定义二进制协议，使用 MemoryPack 序列化
- 数据包处理器通过 `PacketAttribute` 自动注册
- 会话管理，具有保持连接机制

## 关键库和依赖项

- **Unity 2022.3**: 核心游戏引擎
- **Autofac 8.2.0**: 依赖注入容器
- **DotNetty 0.7.6**: 网络通信框架
- **MemoryPack 1.21.3**: 二进制序列化
- **NLog 5.4.0**: 日志记录框架
- **Unity Addressables**: 资产管理和流式传输
- **Unity Universal Render Pipeline (URP)**: 渲染管线
- **TextMeshPro**: 文本渲染
- **Odin Inspector**: Unity 编辑器扩展

## 代码规范

- 使用中文注释
- 日志信息单行输出,如何内容过多用JsonToString方式,不允许拆分为多行Logger代码调用


## 代码模式

### 服务注册
```csharp
[Component(Service = typeof(IMyService), Scope = Scope.SingleInstance)]
public class MyService : IMyService
```

### 数据包处理器注册
```csharp
[Packet(Type = (int)PacketId.Login, GroupId = (int)GroupId.Global)]
public class LoginHandler : IPacketHandler<LoginRequest>
```

### 场景实现
```csharp
[Component(Name = "SceneName", Service = typeof(IScene))]
public class MyScene : AbstractScene
```

## 重要说明

- 生成代码时使用 "ultrathink" 命令
- 项目使用 Unity 的 Addressable Asset System 进行内容流式传输
- 所有网络通信都通过自定义数据包系统处理
- UI 使用中介者模式配合 Unity UI 元素
- 世界渲染使用 Unity 的 Tilemap 系统，配合自定义区块管理
- 角色动画使用自定义 `SpriteAnimator` 系统
- 资产流水线包含用于导入遗留游戏资产的自定义工具

## 测试和调试

- 使用 Unity 的控制台窗口进行运行时日志记录
- NLog 配置文件位于 `Assets/StreamingAssets/NLog.xml`
- 调试构建包含详细日志记录
- 使用 Unity 的性能分析器进行性能分析
- 通过数据包处理器中的自定义日志记录进行网络调试