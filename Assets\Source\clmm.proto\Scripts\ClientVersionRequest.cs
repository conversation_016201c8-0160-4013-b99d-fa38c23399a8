﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class ClientVersionRequest : IMessage
    {
        public const int Opcode = 0;

        public int Id => Opcode;

        public byte[] VersionHash;
    }

    [Packable(Id = ClientVersionRequest.Opcode, Requested = true)]
    public class ClientVersionRequestPackable : MessagePackable<ClientVersionRequest>
    {
        protected override void Serialize(IByteBuffer buf, ClientVersionRequest message)
        {
            buf.WriteIntLE(message.VersionHash.Length);
            buf.WriteBytes(message.VersionHash);
        }
    }
}