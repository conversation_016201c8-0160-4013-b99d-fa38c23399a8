﻿using CLMM.Constant;
using CLMM.Proto;
using CLMM.Repository;
using TMPro;
using UnityEngine.UI;
using Universe;
using ILogger = slf4net.ILogger;

namespace CLMM.UI
{
    [Component(typeof(IMediator), "SelCharInfo")]
    public class SelCharInfoMediator : Mediator
    {
        private static readonly ILogger Logger = LoggerManager.GetCurrentClassLogger();

        private readonly UserRepository _userRepository;
        private int _index;

        private SpriteContainer _classAtlas;
        private Toggle _toggle;
        private Image _emptyImage;
        private TextMeshProUGUI _nameLabel;
        private TextMeshProUGUI _levelLabel;
        private TextMeshProUGUI _classLabel;
        private Image _classFrameImage;
        private Image _classCheckImage;

        public SelCharInfoMediator(UserRepository userRepository)
        {
            _userRepository = userRepository;
        }

        public override void OnCreate()
        {
            _classAtlas = this.UI.GetComponent<SpriteContainer>();

            var vars = this.UI.GetVariables();
            _toggle = vars.Get<Toggle>("Toggle");
            _emptyImage = vars.Get<Image>("EmptyImage");
            _nameLabel = vars.Get<TextMeshProUGUI>("NameLabel");
            _levelLabel = vars.Get<TextMeshProUGUI>("LevelLabel");
            _classLabel = vars.Get<TextMeshProUGUI>("JobLabel");
            _classFrameImage = vars.Get<Image>("ClassFrameImage");
            _classCheckImage = vars.Get<Image>("ClassCheckImage");

            _toggle.onValueChanged.AddListener(OnToggleValueChanged);

            dynamic userdata = this.UI.Userdata;
            _userRepository.SelCharInfos[(int)userdata.Index].Observe(this.UI, OnRefresh);
        }

        private void OnRefresh(SelCharInfo info)
        {
            if (_index == info.Index)
                return;

            _index = info.Index;

            if (info.Level == 0)
            {
                _toggle.gameObject.SetActive(false);
                return;
            }

            _toggle.gameObject.SetActive(true);

            _emptyImage.enabled = false;
            _nameLabel.text = info.Name;
            _levelLabel.text = info.Level.ToString();
            _classLabel.text = ((ClassType)info.Class).ToString();

            _classFrameImage.sprite = _classAtlas.GetSprite($"{(ClassType)info.Class}[Frame]");
            _classCheckImage.sprite = _classAtlas.GetSprite($"{(ClassType)info.Class}[Check]");
        }

        private void OnToggleValueChanged(bool isOn)
        {
            if (!isOn)
                return;

            _userRepository.SetCurrentSelCharInfo(_index);
        }
    }
}