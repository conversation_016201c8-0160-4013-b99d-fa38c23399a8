﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class ObjectAttackNotify : IMessage
    {
        public const int Opcode = 70;

        public int Id => Opcode;
    }

    [Packable(Id = ObjectAttackNotify.Opcode)]
    public class ObjectAttackNotifyPackable : MessagePackable<ObjectAttackNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out ObjectAttackNotify message)
        {
            message = new ObjectAttackNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}