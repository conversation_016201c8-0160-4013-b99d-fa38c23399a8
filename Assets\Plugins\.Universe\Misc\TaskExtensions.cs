using System.Threading.Tasks;
using slf4net;

namespace Universe
{
    public static class TaskExtensions
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(TaskExtensions));

        public static void Continue(this Task task)
        {
            task.ContinueWith(task =>
            {
                if (Logger.IsErrorEnabled)
                    Logger.Error($"{nameof(Continue)} Error in task. Exception - {task.Exception}");
            }, TaskContinuationOptions.OnlyOnFaulted);
        }
    }
}