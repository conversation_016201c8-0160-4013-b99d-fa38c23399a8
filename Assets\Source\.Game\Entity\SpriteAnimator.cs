using UnityEngine;

namespace CLMM
{
    public class SpriteAnimator
    {
        private readonly SpriteRenderer _spriteRenderer;

        private SpriteAnimatorClip.Sequence _sequence;
        private float _frameInterval;
        private float _nextFrameTime;

        public SpriteAnimatorController Controller { get; set; }

        public bool IsPlaying { get; private set; }
        public int FrameIndex { get; set; }
        public int FrameCount { get; set; }

        public SpriteAnimator(SpriteRenderer spriteRenderer)
        {
            _spriteRenderer = spriteRenderer;
        }

        public void Play(int id, int orientation)
        {
            if (!Controller.Clips.TryGetValue(id, out var clip))
                return;

            if (orientation >= clip.Sequences.Length || orientation < 0)
                return;

            var sequence = clip.Sequences[orientation];
            if (sequence == null)
                return;

            _sequence = sequence;
            FrameIndex = 0;
            FrameCount = _sequence.Sprites.Length;
            _frameInterval = clip.FrameInterval * 0.001f;
            _nextFrameTime = Time.realtimeSinceStartup + _frameInterval;

            IsPlaying = true;
        }

        public void Stop()
        {
            IsPlaying = false;
            _sequence = null;
        }

        public bool Update()
        {
            if (!IsPlaying)
                return false;

            if (Time.realtimeSinceStartup < _nextFrameTime)
                return false;

            FrameIndex++;

            if (FrameIndex >= FrameCount)
            {
                Stop();
                return false;
            }

            _spriteRenderer.sprite = _sequence.Sprites[FrameIndex];
            _nextFrameTime += _frameInterval;

            return true;
        }
    }
}