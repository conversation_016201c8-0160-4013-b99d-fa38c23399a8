﻿using System;
using System.IO;
using System.IO.Compression;

namespace CLMM.MirTools
{
    public class MirImage
    {
        public short Width, Height, X, Y, ShadowX, ShadowY;
        public byte Shadow;
        public int Length;
        public byte[] Data;

        public short MaskWidth, MaskHeight, MaskX, MaskY;
        public int MaskLength;
        public bool HasMask;
        public byte[] MaskData;

        public MirImage(BinaryReader reader)
        {
            //read layer 1
            Width = reader.ReadInt16();
            Height = reader.ReadInt16();
            X = reader.ReadInt16();
            Y = reader.ReadInt16();
            ShadowX = reader.ReadInt16();
            ShadowY = reader.ReadInt16();
            Shadow = reader.ReadByte();
            Length = reader.ReadInt32();

            //check if there's a second layer and read it
            HasMask = ((Shadow >> 7) == 1) ? true : false;
            if (HasMask)
            {
                reader.ReadBytes(Length);
                MaskWidth = reader.ReadInt16();
                MaskHeight = reader.ReadInt16();
                MaskX = reader.ReadInt16();
                MaskY = reader.ReadInt16();
                MaskLength = reader.ReadInt32();

                throw new NotImplementedException("HasMask");
            }
        }

        public void LoadTexture(BinaryReader reader)
        {
            var data = reader.ReadBytes(Length);
            using var inputStream = new MemoryStream(data);
            using var outputStream = new MemoryStream();
            using var stream = new GZipStream(inputStream, CompressionMode.Decompress);
            stream.CopyTo(outputStream);
            Data = outputStream.ToArray();

            if (HasMask)
            {
                var maskData = reader.ReadBytes(MaskLength);
                using var maskStream = new GZipStream(new MemoryStream(maskData), CompressionMode.Decompress);
                MaskData = new byte[maskStream.Length];
                maskStream.Read(MaskData, 0, MaskData.Length);
            }
        }
    }
}