﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class ObjectRemoveNotify : IMessage
    {
        public const int Opcode = 26;

        public int Id => Opcode;
    }

    [Packable(Id = ObjectRemoveNotify.Opcode)]
    public class ObjectRemoveNotifyPackable : MessagePackable<ObjectRemoveNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out ObjectRemoveNotify message)
        {
            message = new ObjectRemoveNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}