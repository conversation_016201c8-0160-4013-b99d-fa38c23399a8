﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class DefaultNpcNotify : IMessage
    {
        public const int Opcode = 184;

        public int Id => Opcode;
    }

    [Packable(Id = DefaultNpcNotify.Opcode)]
    public class DefaultNpcNotifyPackable : MessagePackable<DefaultNpcNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out DefaultNpcNotify message)
        {
            message = new DefaultNpcNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}