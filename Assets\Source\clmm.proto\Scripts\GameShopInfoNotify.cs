﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    [Protocol(LogLevel.DEBUG)]
    public class GameShopInfoNotify : IMessage
    {
        public const int Opcode = 248;

        public int Id => Opcode;
    }

    [Packable(Id = GameShopInfoNotify.Opcode)]
    public class GameShopInfoNotifyPackable : MessagePackable<GameShopInfoNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out GameShopInfoNotify message)
        {
            message = new GameShopInfoNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}