﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class RemoveBuffNotify : IMessage
    {
        public const int Opcode = 143;

        public int Id => Opcode;
    }

    [Packable(Id = RemoveBuffNotify.Opcode)]
    public class RemoveBuffNotifyPackable : MessagePackable<RemoveBuffNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out RemoveBuffNotify message)
        {
            message = new RemoveBuffNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}