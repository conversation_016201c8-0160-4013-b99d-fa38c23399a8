﻿using System;
using System.IO;
using UnityEngine;

namespace CLMM.MirTools
{
    public class MirCellInfo
    {
        public short BackIndex;
        public int BackImage;
        public short MiddleIndex;
        public int MiddleImage;
        public short FrontIndex;
        public int FrontImage;

        public byte DoorIndex;
        public byte DoorOffset;

        public byte FrontAnimationFrame;
        public byte FrontAnimationTick;

        public byte MiddleAnimationFrame;
        public byte MiddleAnimationTick;

        public short TileAnimationImage;
        public short TileAnimationOffset;
        public byte TileAnimationFrames;

        public byte Light;
        public bool FishingCell;
    }

    public class MirMapInfo
    {
        public int Width;
        public int Height;

        public MirCellInfo[,] CellInfos;

        public MirMapInfo(string filename)
        {
            var data = File.ReadAllBytes(filename);
            if (data[2] == 0x43 && data[3] == 0x23)
                LoadMapType100(data);
            // else
            //     Debug.LogWarning($"Unsupported map type. File: {filename}");
            // LoadMapType0(data);
        }

        private void LoadMapType100(byte[] data)
        {
            var offset = 4;
            if (data[0] != 1 || data[1] != 0) return; //only support version 1 atm
            Width = BitConverter.ToInt16(data, offset);
            offset += 2;
            Height = BitConverter.ToInt16(data, offset);
            CellInfos = new MirCellInfo[Width, Height];
            offset = 8;
            for (var x = 0; x < Width; x++)
            for (var y = 0; y < Height; y++)
            {
                CellInfos[x, y] = new MirCellInfo();
                CellInfos[x, y].BackIndex = (short)BitConverter.ToInt16(data, offset);
                offset += 2;
                CellInfos[x, y].BackImage = (int)BitConverter.ToInt32(data, offset);
                offset += 4;
                CellInfos[x, y].MiddleIndex = (short)BitConverter.ToInt16(data, offset);
                offset += 2;
                CellInfos[x, y].MiddleImage = (short)BitConverter.ToInt16(data, offset);
                offset += 2;
                CellInfos[x, y].FrontIndex = (short)BitConverter.ToInt16(data, offset);
                offset += 2;
                CellInfos[x, y].FrontImage = (short)BitConverter.ToInt16(data, offset);
                offset += 2;
                CellInfos[x, y].DoorIndex = (byte)(data[offset++] & 0x7F);
                CellInfos[x, y].DoorOffset = data[offset++];
                CellInfos[x, y].FrontAnimationFrame = data[offset++];
                CellInfos[x, y].FrontAnimationTick = data[offset++];
                CellInfos[x, y].MiddleAnimationFrame = data[offset++];
                CellInfos[x, y].MiddleAnimationTick = data[offset++];
                CellInfos[x, y].TileAnimationImage = (short)BitConverter.ToInt16(data, offset);
                offset += 2;
                CellInfos[x, y].TileAnimationOffset = (short)BitConverter.ToInt16(data, offset);
                offset += 2;
                CellInfos[x, y].TileAnimationFrames = data[offset++];
                CellInfos[x, y].Light = data[offset++];

                if (CellInfos[x, y].Light >= 100 && CellInfos[x, y].Light <= 119)
                    CellInfos[x, y].FishingCell = true;
            }
        }

        private void LoadMapType0(byte[] data)
        {
            var offset = 0;
            Width = BitConverter.ToInt16(data, offset);
            offset += 2;
            Height = BitConverter.ToInt16(data, offset);
            offset = 52;
            CellInfos = new MirCellInfo[Width, Height];

            for (var x = 0; x < Width; x++)
            for (var y = 0; y < Height; y++)
            {
                CellInfos[x, y] = new MirCellInfo();
                CellInfos[x, y].BackIndex = 0;
                CellInfos[x, y].MiddleIndex = 1;
                CellInfos[x, y].BackImage = BitConverter.ToInt16(data, offset);
                offset += 2;
                CellInfos[x, y].MiddleImage = BitConverter.ToInt16(data, offset);
                offset += 2;
                CellInfos[x, y].FrontImage = BitConverter.ToInt16(data, offset);
                offset += 2;
                CellInfos[x, y].DoorIndex = (byte)(data[offset++] & 0x7F);
                CellInfos[x, y].DoorOffset = data[offset++];
                CellInfos[x, y].FrontAnimationFrame = data[offset++];
                CellInfos[x, y].FrontAnimationTick = data[offset++];
                CellInfos[x, y].FrontIndex = (short)(data[offset++] + 2);
                CellInfos[x, y].Light = data[offset++];

                if ((CellInfos[x, y].BackImage & 0x8000) != 0)
                    CellInfos[x, y].BackImage = (CellInfos[x, y].BackImage & 0x7FFF) | 0x20000000;

                if (CellInfos[x, y].Light >= 100 && CellInfos[x, y].Light <= 119)
                    CellInfos[x, y].FishingCell = true;
            }
        }
    }
}