using System;
using DotNetty.Buffers;
using DotNetty.Codecs;
using DotNetty.Transport.Channels;
using slf4net;

namespace Ock
{
    public class ProtocolEncoder : MessageToByteEncoder<IMessage>
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(ProtocolEncoder));


        private readonly IProtocolSerializer _protocolSerializer;

        public ProtocolEncoder(IProtocolSerializer protocolSerializer)
        {
            _protocolSerializer = protocolSerializer;
        }

        protected override void Encode(
            IChannelHandlerContext context,
            IMessage message,
            IByteBuffer output)
        {
            var offset = output.WriterIndex;
            output.SetWriterIndex(offset + sizeof(ushort));

            try
            {
                _protocolSerializer.Serialize(output, message);
            }
            catch (Exception e)
            {
                output.SetWriterIndex(offset);

                if (Logger.IsErrorEnabled)
                    Logger.Error($"{nameof(Encode)}: Serialize.\r\nException - {e}");

                return;
            }

            output.SetUnsignedShortLE(offset, (ushort)(output.WriterIndex - offset));
        }
    }
}