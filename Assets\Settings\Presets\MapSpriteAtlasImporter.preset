%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!181963792 &2655988077585873504
Preset:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: MapSpriteAtlasImporter
  m_TargetType:
    m_NativeTypeID: 1210832254
    m_ManagedTypePPtr: {fileID: 0}
    m_ManagedTypeFallback: 
  m_Properties:
  - target: {fileID: 0}
    propertyPath: m_ExternalObjects.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TextureSettings.anisoLevel
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TextureSettings.compressionQuality
    value: 50
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TextureSettings.maxTextureSize
    value: 2048
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TextureSettings.textureCompression
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TextureSettings.filterMode
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TextureSettings.generateMipMaps
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TextureSettings.readable
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TextureSettings.crunchedCompression
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TextureSettings.sRGB
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.size
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[0].m_BuildTarget
    value: DefaultTexturePlatform
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[0].m_MaxTextureSize
    value: 2048
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[0].m_ResizeAlgorithm
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[0].m_TextureFormat
    value: -1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[0].m_TextureCompression
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[0].m_CompressionQuality
    value: 100
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[0].m_CrunchedCompression
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[0].m_AllowsAlphaSplitting
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[0].m_Overridden
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[0].m_IgnorePlatformSupport
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[0].m_AndroidETC2FallbackOverride
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[0].m_ForceMaximumCompressionQuality_BC6H_BC7
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PackingSettings.padding
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PackingSettings.blockOffset
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PackingSettings.allowAlphaSplitting
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PackingSettings.enableRotation
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PackingSettings.enableTightPacking
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PackingSettings.enableAlphaDilation
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SecondaryTextureSettings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_VariantMultiplier
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_BindAsDefault
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UserData
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_AssetBundleName
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_AssetBundleVariant
    value: 
    objectReference: {fileID: 0}
  m_ExcludedProperties: []
