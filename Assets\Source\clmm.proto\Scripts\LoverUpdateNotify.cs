﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class LoverUpdateNotify : IMessage
    {
        public const int Opcode = 244;

        public int Id => Opcode;
    }

    [Packable(Id = LoverUpdateNotify.Opcode)]
    public class LoverUpdateNotifyPackable : MessagePackable<LoverUpdateNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out LoverUpdateNotify message)
        {
            message = new LoverUpdateNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}