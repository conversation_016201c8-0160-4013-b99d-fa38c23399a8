﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class BaseStatsInfoNotify : IMessage
    {
        public const int Opcode = 160;

        public int Id => Opcode;
    }

    [Packable(Id = BaseStatsInfoNotify.Opcode)]
    public class BaseStatsInfoNotifyPackable : MessagePackable<BaseStatsInfoNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out BaseStatsInfoNotify message)
        {
            message = new BaseStatsInfoNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}