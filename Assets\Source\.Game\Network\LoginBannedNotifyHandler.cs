using CLMM.Constant;
using CLMM.Proto;
using CLMM.Repository;
using slf4net;
using Universe;
using ILogger = slf4net.ILogger;
using JsonConvert = Newtonsoft.Json.JsonConvert;

namespace CLMM.Network
{
    [Component]
    [Handler((int)MessageId.LoginBannedNotify, Tag = (int)GroupId.Login)]
    public class LoginBannedNotifyHandler : MessageHandler<LoginBannedNotify>
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(LoginBannedNotifyHandler));

        private readonly UserRepository _userRepository;

        public LoginBannedNotifyHandler(UserRepository userRepository)
        {
            _userRepository = userRepository;
        }

        protected override void Handle(ISession session, LoginBannedNotify message)
        {
            if (Logger.IsDebugEnabled)
                Logger.Debug($"{nameof(Handle)}: -I {nameof(LoginBannedNotify)} {JsonConvert.SerializeObject(message)}");

            _userRepository.OnLoginBanned(message.Reason, message.Expiry);
        }
    }
}