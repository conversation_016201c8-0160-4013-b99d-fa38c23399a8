﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class ObjectMonsterNotify : IMessage
    {
        public const int Opcode = 69;

        public int Id => Opcode;
    }

    [Packable(Id = ObjectMonsterNotify.Opcode)]
    public class ObjectMonsterNotifyPackable : MessagePackable<ObjectMonsterNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out ObjectMonsterNotify message)
        {
            message = new ObjectMonsterNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}