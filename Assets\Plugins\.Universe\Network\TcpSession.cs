﻿using System;
using System.Threading.Tasks;
using DotNetty.Transport.Channels.Sockets;
using Newtonsoft.Json;
using slf4net;

namespace Universe
{
    public class TcpSession : TcpSocketChannel, ISession
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(TcpSession));

        private IMessageFilter _filter;

        public bool IsConnected => this.Active;

        public void Initialize(IMessageFilter filter)
        {
            _filter = filter;
        }

        public Task SendAsync(IMessage message)
        {
            if (Logger.IsInfoEnabled)
            {
                var type = message.GetType();
                var logLevel = _filter.GetLogLevel(type);
                if (logLevel == LogLevel.None && logLevel <= LogLevel.INFO)
                    Logger.Info($"{nameof(WriteAsync)}: -I {type.Name} {JsonConvert.SerializeObject(message)}");
            }

            if (!IsConnected)
                return Task.FromException(new Exception("Session is not connected"));

            return WriteAndFlushAsync(message);
        }
    }
}