﻿using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Autofac;
using Universe;

namespace CLMM.Scene
{
    public abstract class BaseScene : IScene
    {
        public abstract int Tag { get; }
        protected abstract string[] LoadUIViews { get; }
        protected abstract string[] UnloadUIViews { get; }

        protected readonly ILifetimeScope _container;

        protected readonly Dictionary<int, IMessageHandler> _packetHandlers = new();

        public BaseScene(ILifetimeScope container)
        {
            _container = container;

            var types = Assembly.GetExecutingAssembly()
                .GetTypes()
                .Where(type => typeof(IMessageHandler).IsAssignableFrom(type)
                               && !type.IsAbstract
                               && !type.IsInterface);

            foreach (var type in types)
            {
                var attribute = type.GetCustomAttribute<HandlerAttribute>();
                if (attribute == null || (attribute.Tag & Tag) == 0)
                    continue;

                var handler = (IMessageHandler)_container.Resolve(type);
                _packetHandlers.Add(attribute.Id, handler);
            }
        }

        public virtual void Activate()
        {
            NetworkRegister();

            var window = _container.Resolve<IUIWindow>();
            foreach (var name in LoadUIViews)
            {
                var view = _container.Resolve<IUIView>(
                    new NamedParameter("name", name));
                view.SetParent(window);
            }
        }

        public virtual void Deactivate()
        {
            NetworkUnregister();

            var window = _container.Resolve<IUIWindow>();
            foreach (var name in UnloadUIViews)
            {
                var ui = window.Children.FirstOrDefault(ui => ui.Name == name);
                if (ui != null)
                    ui.Dispose();
            }
        }

        private void NetworkRegister()
        {
            var dispatcher = _container.Resolve<IMessageDispatcher>();
            foreach (var (id, handler) in _packetHandlers)
                dispatcher.Register(id, handler);
        }

        private void NetworkUnregister()
        {
            var dispatcher = _container.Resolve<IMessageDispatcher>();
            foreach (var (id, handler) in _packetHandlers)
                dispatcher.Unregister(id, handler);
        }
    }
}