using System;
using System.Collections.Generic;
using System.Linq;
using CLMM.Domain;
using CLMM.Proto;
using Ock;
using slf4net;
using UnityEngine;
using GameStateChangedHandler = CLMM.Facade.GameStateChangedHandler;
using ILogger = slf4net.ILogger;

namespace CLMM
{
    public class GameBehavior : MonoBehaviour
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(GameBehavior));


        public Context Context { get; } = new("Game",
            new[]
            {
                typeof(Context).Assembly,
                typeof(GameStateMachine).Assembly,
                typeof(GameStateChangedHandler).Assembly
            });

        private IList<IService> _services;

        private void Awake()
        {
            if (Logger.IsInfoEnabled)
                Logger.Info("Initializing...");

            try
            {
                _services = Context.Resolve<IEnumerable<IService>>().ToList();
                Context.RegisterPackables<IProtocolSerializer, IMessagePackable>(typeof(ConnectedNotifyPackable).Assembly);
                Context.RegisterFilter<IMessageFilter, IMessage>(typeof(ConnectedNotify).Assembly);
                Context.RegisterHandlers<IMessageDispatcher, IMessageHandler>((int)GameState.ANY);
                Context.RegisterHandlers<IEventDispatcher, IEventHandler>((int)GameState.ANY);

                foreach (var service in _services)
                    service.Initialize();
            }
            catch (Exception e)
            {
                this.gameObject.SetActive(false);

                if (_services != null)
                {
                    foreach (var service in _services)
                        service.Shutdown();
                }
                _services = null;

                if (Logger.IsErrorEnabled)
                    Logger.Error($"{nameof(Start)}: failed.\r\nException - {e}");

                return;
            }

            if (Logger.IsInfoEnabled)
                Logger.Info("Initialized.");
        }

        private void Start()
        {
            Context.Resolve<GameStateMachine>().Start(GameState.LOGIN);
        }

        private void Update()
        {
            foreach (var service in _services)
                service.Execute();
        }

        private void OnDestroy()
        {
            if (_services != null)
            {
                foreach (var service in _services)
                    service.Shutdown();
            }
            _services = null;
        }
    }
}