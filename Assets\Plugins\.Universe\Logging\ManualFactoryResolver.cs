﻿using slf4net;

namespace Universe
{
    public class ManualFactoryResolver : IFactoryResolver, ISlf4netServiceProviderResolver
    {
        private readonly ISlf4netServiceProvider _provider;

        public ManualFactoryResolver(ISlf4netServiceProvider provider)
        {
            _provider = provider;
        }

        public ILoggerFactory GetFactory()
        {
            return GetProvider()?.GetLoggerFactory();
        }

        public ISlf4netServiceProvider GetProvider()
        {
            return _provider;
        }
    }
}