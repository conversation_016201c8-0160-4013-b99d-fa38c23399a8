﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.Profiling;
using UnityEngine.ResourceManagement.AsyncOperations;
using UnityEngine.Tilemaps;
using Object = UnityEngine.Object;

namespace CLMM
{
    public class Chunk : IDisposable
    {
        private Transform _transform;

        private readonly TileBase[] _tileArray = new TileBase[World.ChunkHalfSize * World.ChunkHalfSize];
        private BoundsInt _position;

        private readonly TileBase[][] _tile2Array = new TileBase[2][];
        private readonly Vector3Int[][] _position2Array = new Vector3Int[2][];

        private AsyncOperationHandle<IList<TileAtlas>> _atlasesHandle;

        public Vector2Int Index { get; private set; }

        public Chunk()
        {
            var go = new GameObject("Chunk");
            go.layer = World.TileLayer;
            _transform = go.transform;
            _transform.parent = World.ObjectTransform;
        }

        public void Dispose()
        {
            if (_transform is not null)
            {
                Object.Destroy(_transform.gameObject);
                _transform = null;
            }
        }

        public void Load(ChunkInfo info)
        {
            Index = info.Index;
            _transform.gameObject.name = $"Chunk ({Index.x} {Index.y})";
            _transform.localPosition = new Vector3(
                Index.x * World.ChunkSize * World.TileSize.x,
                -Index.y * World.ChunkSize * World.TileSize.y, 0);

            Profiler.BeginSample("Load Palettes");
            var keys = new List<string>();
            foreach (var palette in info.Palettes)
                keys.Add($"Map/Atlas/{palette}.tileatlas");
            _atlasesHandle = Addressables.LoadAssetsAsync<TileAtlas>(keys, null, Addressables.MergeMode.Union, false);
            _atlasesHandle.WaitForCompletion();
            if (_atlasesHandle.Status != AsyncOperationStatus.Succeeded)
                throw new Exception($"Failed to load RefSpriteAtlas: {Index}");
            var palettes = _atlasesHandle.Result;
            Profiler.EndSample();

            Profiler.BeginSample("FillLayer");
            FillBaseLayer(info, palettes);
            FillTileLayer(0, info, palettes);
            FillTileLayer(1, info, palettes);
            FillObjectLayer(info, palettes);
            Profiler.EndSample();
        }

        public void Unload()
        {
            EraseObjectLayer();
            EraseTileLayer(1);
            EraseTileLayer(0);
            EraseBaseLayer();

            if (_atlasesHandle.IsValid())
                Addressables.Release(_atlasesHandle);
        }

        private void FillBaseLayer(ChunkInfo info, IList<TileAtlas> palettes)
        {
            const int size = World.ChunkHalfSize;

            var tileIds = info.TileIds;
            for (var y = 0; y < size; y++)
            {
                for (var x = 0; x < size; x++)
                {
                    var (pIndex, imageId) = tileIds[x, y];
                    _tileArray[(size - 1 - y) * size + x] = imageId >= 0
                        ? palettes[pIndex].Tiles[imageId]
                        : null;
                }
            }

            _position = new BoundsInt(
                Index.x * size, -(Index.y + 1) * size, 0,
                size, size, 1);

            World.Tilemap.SetTilesBlock(_position, _tileArray);
        }

        private void EraseBaseLayer()
        {
            Array.Clear(_tileArray, 0, _tileArray.Length);
            World.Tilemap.SetTilesBlock(_position, _tileArray);
        }

        private void FillTileLayer(int layer, ChunkInfo info, IList<TileAtlas> palettes)
        {
            const int size = World.ChunkSize;

            var objects = info.Object2Array[layer];
            var count = objects.Length;
            var positionArray = new Vector3Int[count];
            var tileArray = new TileBase[count];
            for (var i = 0; i < count; i++)
            {
                var (x, y, pIndex, imageId) = objects[i];
                positionArray[i] = new Vector3Int(x + Index.x * size, -(y + 1 + Index.y * size), 0);
                tileArray[i] = palettes[pIndex].Tiles[imageId];
            }
            _position2Array[layer] = positionArray;
            _tile2Array[layer] = tileArray;

            World.Tilemaps[layer].SetTiles(positionArray, tileArray);
        }

        private void EraseTileLayer(int layer)
        {
            var positionArray = _position2Array[layer];
            var tileArray = _tile2Array[layer];
            Array.Clear(tileArray, 0, tileArray.Length);
            World.Tilemaps[layer].SetTiles(positionArray, tileArray);
        }

        private void FillObjectLayer(ChunkInfo info, IList<TileAtlas> palettes)
        {
            var objects = info.Object2Array[2];
            foreach (var (x, y, pIndex, imageId) in objects)
            {
                var go = Object.Instantiate(World.SpritePrefab);
                go.name = $"Sprite ({x} {y})";
                var transform = go.transform;
                transform.parent = _transform;
                transform.localPosition = new Vector3(
                    x * World.TileSize.x,
                    -(y + 1) * World.TileSize.y,
                    -(y + Index.y * World.ChunkSize) * 0.001f);
                var spriteRenderer = go.GetComponent<SpriteRenderer>();
                var tile = (Tile)palettes[pIndex].Tiles[imageId];
                spriteRenderer.sprite = tile.sprite;
            }
        }

        private void EraseObjectLayer() { }
    }
}