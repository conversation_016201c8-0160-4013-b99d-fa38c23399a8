﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class DisconnectNotify : IMessage
    {
        public const int Opcode = 2;

        public int Id => Opcode;

        public byte Reason;
    }

    [Packable(Id = DisconnectNotify.Opcode)]
    public class DisconnectNotifyPackable : MessagePackable<DisconnectNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out DisconnectNotify message)
        {
            message = new DisconnectNotify();
            message.Reason = buf.ReadByte();
        }
    }
}