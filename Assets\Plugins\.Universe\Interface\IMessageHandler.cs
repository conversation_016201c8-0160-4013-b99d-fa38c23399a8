﻿namespace Universe
{
    public interface IMessageHandler
    {
        void Handle(ISession session, object message);
    }

    public abstract class MessageHandler<T> : IMessageHandler where T : IMessage
    {
        protected abstract void Handle(ISession session, T message);

        public void Handle(ISession session, object message)
        {
            Handle(session, (T)message);
        }
    }
}