using CLMM.Constant;
using CLMM.Proto;
using slf4net;
using Universe;
using ILogger = slf4net.ILogger;

namespace CLMM.Network
{
    [Component]
    [<PERSON><PERSON>(WriterIdleNotify.Opcode, Tag = (int)GroupId.Global)]
    public class WriterIdleNotifyHandler : MessageHandler<WriterIdleNotify>
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(WriterIdleNotifyHandler));

        protected override void Handle(ISession session, WriterIdleNotify message)
        {
            if (Logger.IsTraceEnabled)
                Logger.Trace($"{nameof(Handle)}: -I {nameof(WriterIdleNotify)}");

            var request = new KeepAliveRequest();
            request.Time = 0;

            session.SendAsync(request).Continue();
        }
    }
}