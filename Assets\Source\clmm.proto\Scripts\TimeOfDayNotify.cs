﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class TimeOfDayNotify : IMessage
    {
        public const int Opcode = 59;

        public int Id => Opcode;
    }

    [Packable(Id = TimeOfDayNotify.Opcode)]
    public class TimeOfDayNotifyPackable : MessagePackable<TimeOfDayNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out TimeOfDayNotify message)
        {
            message = new TimeOfDayNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}