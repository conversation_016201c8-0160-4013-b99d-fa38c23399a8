﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class ObjectWalkNotify : IMessage
    {
        public const int Opcode = 28;

        public int Id => Opcode;
    }

    [Packable(Id = ObjectWalkNotify.Opcode)]
    public class ObjectWalkNotifyPackable : MessagePackable<ObjectWalkNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out ObjectWalkNotify message)
        {
            message = new ObjectWalkNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}