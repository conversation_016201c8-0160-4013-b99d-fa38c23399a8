namespace Ock
{
    public interface IEventHandler
    {
        void Handle(IEvent @event);
    }

    public abstract class EventHandler : IEventHandler
    {
        protected abstract void Execute();

        public void Handle(IEvent @event)
            => Execute();
    }

    public abstract class EventHandler<T> : IEventHandler where T : IEvent
    {
        protected abstract void Execute(T @event);

        public void Handle(IEvent @event)
            => Execute((T)@event);
    }
}