﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class StartGameResponse : IMessage
    {
        public const int Opcode = 14;

        public int Id => Opcode;

        public byte Result;
        public int Resolution;
    }

    [Packable(Id = StartGameResponse.Opcode)]
    public class StartGameResponsePackable : MessagePackable<StartGameResponse>
    {
        protected override void Deserialize(IByteBuffer buf, out StartGameResponse message)
        {
            message = new StartGameResponse();
            message.Result = buf.ReadByte();
            message.Resolution = buf.ReadIntLE();
        }
    }
}