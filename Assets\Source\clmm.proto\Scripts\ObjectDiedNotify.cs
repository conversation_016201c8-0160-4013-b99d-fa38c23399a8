﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class ObjectDiedNotify : IMessage
    {
        public const int Opcode = 79;

        public int Id => Opcode;
    }

    [Packable(Id = ObjectDiedNotify.Opcode)]
    public class ObjectDiedNotifyPackable : MessagePackable<ObjectDiedNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out ObjectDiedNotify message)
        {
            message = new ObjectDiedNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}