﻿using System.Collections.Generic;
using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class LoginSuccessNotify : IMessage
    {
        public const int Opcode = 9;

        public int Id => Opcode;

        public List<SelCharInfo> SelCharInfos = new(4);
    }

    [Packable(Id = LoginSuccessNotify.Opcode)]
    public class LoginSuccessNotifyPackable : MessagePackable<LoginSuccessNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out LoginSuccessNotify message)
        {
            message = new LoginSuccessNotify();
            var count = buf.ReadIntLE();
            message.SelCharInfos = new List<SelCharInfo>();
            for (var i = 0; i < count; i++)
            {
                var info = buf.ReadSelCharInfo();
                message.SelCharInfos.Add(info);
            }
        }
    }
}