﻿using NLog;
using NLog.Targets;
using UnityEngine;

namespace Universe
{
    [Target("UnityConsole")]
    public class UnityConsoleTarget : TargetWithLayoutHeaderAndFooter
    {
        protected override void Write(LogEventInfo logEvent)
        {
            var logMessage = this.Layout.Render(logEvent);
#if UNITY_EDITOR
            logMessage = "♪" + logMessage;
#endif
            if (logEvent.Level == NLog.LogLevel.Debug
                || logEvent.Level == NLog.LogLevel.Info
                || logEvent.Level == NLog.LogLevel.Trace)
                Debug.Log(logMessage);
            else if (logEvent.Level == NLog.LogLevel.Warn)
                Debug.LogWarning(logMessage);
            else if (logEvent.Level == NLog.LogLevel.Error
                     || logEvent.Level == NLog.LogLevel.Fatal)
                Debug.LogError(logMessage);
        }
    }
}