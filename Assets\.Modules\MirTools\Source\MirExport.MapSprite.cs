﻿using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEditor.U2D;
using UnityEngine;
using UnityEngine.U2D;
using Object = UnityEngine.Object;

namespace CLMM.MirTools
{
    public partial class MirExport
    {
        [MenuItem("MitTools/Export All Map SpriteAtlases")]
        private static void GenerateAllMapSpriteAtlases()
        {
            EditorUtility.DisplayProgressBar("Exporting All Map SpriteAtlases", "Processing ...", 0);

            var mapping = new Dictionary<string, string>
            {
                { "Map/Texture", "Map/Atlas" }
            };

            try
            {
                foreach (var (key, value) in mapping)
                {
                    var guids = AssetDatabase.FindAssets("t:Texture", new[] { $"Assets/Content/{key}" });

                    var index = 0;
                    var count = guids.Length;
                    foreach (var guid in guids)
                    {
                        var texturePath = AssetDatabase.GUIDToAssetPath(guid);

                        EditorUtility.DisplayProgressBar(
                            $"Generating Map SpriteAtlas({++index}/{count})",
                            "Processing " + texturePath,
                            (float)index / count);


                        var atlasPath = texturePath.Replace(key, value)
                            .Replace(Path.GetExtension(texturePath), ".spriteatlasv2");
                        var atlasAsset = SpriteAtlasAsset.Load(atlasPath);
                        if (atlasAsset != null)
                            continue;

                        atlasAsset = new SpriteAtlasAsset();
                        var folder = AssetDatabase.LoadAssetAtPath(texturePath, typeof(Object));
                        atlasAsset.Add(new[] { folder });

                        var dirPath = Path.GetDirectoryName(atlasPath);
                        if (!Directory.Exists(dirPath))
                            Directory.CreateDirectory(dirPath);

                        SpriteAtlasAsset.Save(atlasAsset, atlasPath);
                    }
                }
            }
            finally
            {
                EditorUtility.ClearProgressBar();
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
                Resources.UnloadUnusedAssets();
            }
        }

        [MenuItem("MitTools/Export All Map RefSpriteAtlases")]
        private static void GenerateAllRefMapSpriteAtlases()
        {
            EditorUtility.DisplayProgressBar("Exporting All Map RefSpriteAtlases", "Processing ...", 0);

            var mapping = new Dictionary<string, string>
            {
                { "Map/Atlas", "Map/Atlas" }
            };

            try
            {
                foreach (var (key, value) in mapping)
                {
                    var guids = AssetDatabase.FindAssets("t:SpriteAtlas", new[] { $"Assets/Content/{key}" });

                    var index = 0;
                    var count = guids.Length;
                    foreach (var guid in guids)
                    {
                        var assetPath = AssetDatabase.GUIDToAssetPath(guid);

                        EditorUtility.DisplayProgressBar(
                            $"Generating Map RefSpriteAtlas({++index}/{count})",
                            "Processing " + assetPath,
                            (float)index / count);

                        var refAssetPath = assetPath.Replace(key, value)
                            .Replace(Path.GetExtension(assetPath), ".refspriteatlas.asset");
                        var refSpriteAtlas = AssetDatabase.LoadAssetAtPath<RefSpriteAtlas>(refAssetPath);
                        if (refSpriteAtlas != null)
                            continue;

                        var sprites = new List<Sprite>();
                        var spriteAtlas = AssetDatabase.LoadAssetAtPath<SpriteAtlas>(assetPath);
                        var packables = spriteAtlas.GetPackables();
                        foreach (var packable in packables)
                        {
                            if (packable is Texture2D texture)
                            {
                                var texturePath = AssetDatabase.GetAssetPath(texture);
                                sprites.AddRange(AssetDatabase.LoadAllAssetsAtPath(texturePath)
                                    .OfType<Sprite>());
                            }
                        }

                        refSpriteAtlas = ScriptableObject.CreateInstance<RefSpriteAtlas>();
                        refSpriteAtlas.Atlas = spriteAtlas;
                        foreach (var sprite in sprites)
                        {
                            var id = int.Parse(sprite.name);
                            refSpriteAtlas.Sprites.Add(id, sprite);
                        }

                        var dirPath = Path.GetDirectoryName(refAssetPath);
                        if (!Directory.Exists(dirPath))
                            Directory.CreateDirectory(dirPath);

                        AssetDatabase.CreateAsset(refSpriteAtlas, refAssetPath);
                    }
                }
            }
            finally
            {
                EditorUtility.ClearProgressBar();
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
                Resources.UnloadUnusedAssets();
            }
        }
    }
}