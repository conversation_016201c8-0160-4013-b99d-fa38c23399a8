using System;
using System.Collections.Generic;
using DotNetty.Buffers;
using DotNetty.Codecs;
using DotNetty.Transport.Channels;
using slf4net;

namespace Universe
{
    public class ProtocolDecoder : MessageToMessageDecoder<IByteBuffer>
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(ProtocolDecoder));

        private readonly IProtocolSerializer _serializer;

        public ProtocolDecoder(IProtocolSerializer serializer)
        {
            _serializer = serializer;
        }

        protected override void Decode(
            IChannelHandlerContext context,
            IByteBuffer input,
            List<object> output)
        {
            object packet = null;
            try
            {
                packet = _serializer.Deserialize(input);
                if (input.ReadableBytes > 0)
                {
                    if (Logger.IsWarnEnabled)
                        Logger.Warn($"{nameof(Decode)}: Deserialized input.ReadableBytes. -I {input.ReadableBytes}");
                }
            }
            catch (Exception e)
            {
                if (Logger.IsErrorEnabled)
                    Logger.Error($"{nameof(Decode)}: Error deserializing packet.\r\nException - {e}");
            }

            if (packet != null)
                output.Add(packet);
        }
    }
}