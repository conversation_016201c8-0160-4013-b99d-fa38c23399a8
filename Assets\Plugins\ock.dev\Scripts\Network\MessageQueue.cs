using System.Collections.Concurrent;
using System.Collections.Generic;
using Autofac.Annotation;

namespace Ock
{
    [Component(typeof(IMessageQueue), AutofacScope = AutofacScope.InstancePerLifetimeScope)]
    public class MessageQueue : ConcurrentQueue<(ISession, IMessage)>, IMessageQueue
    {
        public void SwapTo(ICollection<(ISession, IMessage)> collection)
        {
            while (TryDequeue(out var result))
                collection.Add(result);
        }
    }
}