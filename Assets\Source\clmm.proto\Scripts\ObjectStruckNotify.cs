﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class ObjectStruckNotify : IMessage
    {
        public const int Opcode = 72;

        public int Id => Opcode;
    }

    [Packable(Id = ObjectStruckNotify.Opcode)]
    public class ObjectStruckNotifyPackable : MessagePackable<ObjectStruckNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out ObjectStruckNotify message)
        {
            message = new ObjectStruckNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}