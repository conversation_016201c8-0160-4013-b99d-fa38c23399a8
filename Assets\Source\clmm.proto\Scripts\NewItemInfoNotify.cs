﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    [Protocol(LogLevel.DEBUG)]
    public class NewItemInfoNotify : IMessage
    {
        public const int Opcode = 32;

        public int Id => Opcode;
    }

    [Packable(Id = NewItemInfoNotify.Opcode)]
    public class NewItemInfoNotifyPackable : MessagePackable<NewItemInfoNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out NewItemInfoNotify message)
        {
            message = new NewItemInfoNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}