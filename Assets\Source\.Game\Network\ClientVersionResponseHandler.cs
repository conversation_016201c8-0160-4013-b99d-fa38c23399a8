using CLMM.Constant;
using CLMM.Proto;
using CLMM.Repository;
using slf4net;
using Universe;
using JsonConvert = Newtonsoft.Json.JsonConvert;

namespace CLMM.Network
{
    [Component]
    [Handler((int)MessageId.ClientVersionResponse, Tag = (int)GroupId.Login)]
    public class ClientVersionResponseHandler : MessageHandler<ClientVersionResponse>
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(ClientVersionResponseHandler));

        private readonly UserRepository _userRepository;

        public ClientVersionResponseHandler(UserRepository userRepository)
        {
            _userRepository = userRepository;
        }

        protected override void Handle(ISession session, ClientVersionResponse message)
        {
            if (Logger.IsDebugEnabled)
                Logger.Debug($"{nameof(Handle)}: -I {nameof(ClientVersionResponse)} {JsonConvert.SerializeObject(message)}");

            _userRepository.OnClientVersion(message.Result);
        }
    }
}