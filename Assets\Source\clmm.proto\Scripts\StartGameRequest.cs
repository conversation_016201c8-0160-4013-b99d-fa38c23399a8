﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class StartGameRequest : IMessage
    {
        public const int Opcode = 8;

        public int Id => Opcode;

        public int SelCharIndex;
    }

    [Packable(Id = StartGameRequest.Opcode, Requested = true)]
    public class StartGameRequestPackable : MessagePackable<StartGameRequest>
    {
        protected override void Serialize(IByteBuffer buf, StartGameRequest message)
        {
            buf.WriteIntLE(message.SelCharIndex);
        }
    }
}