﻿using System;
using System.Buffers;
using System.Text;
using DotNetty.Buffers;

namespace CLMM.Proto
{
    public static class ByteBufferExtensions
    {
        public static void Write7BitEncodedInt(this IByteBuffer buf, int value)
        {
            var uValue = (uint)value;
            while (uValue > 0x7Fu)
            {
                buf.WriteByte((byte)(uValue | ~0x7Fu));
                uValue >>= 7;
            }

            buf.WriteByte((byte)uValue);
        }

        public static int Read7BitEncodedInt(this IByteBuffer buf)
        {
            uint result = 0;
            byte byteReadJustNow;

            const int maxBytesWithoutOverflow = 4;
            for (var shift = 0; shift < maxBytesWithoutOverflow * 7; shift += 7)
            {
                byteReadJustNow = buf.ReadByte();
                result |= (byteReadJustNow & 0x7Fu) << shift;

                if (byteReadJustNow <= 0x7Fu)
                    return (int)result;
            }

            byteReadJustNow = buf.ReadByte();
            if (byteReadJustNow > 0b_1111u)
                throw new FormatException("Too many bytes in what should have been a 7 bit encoded Int32.");

            result |= (uint)byteReadJustNow << maxBytesWithoutOverflow * 7;
            return (int)result;
        }

        public static void WriteFixedString(this IByteBuffer buf, string value, Encoding encoding)
        {
            if (value.Length > 64 * 1024 / 3)
                throw new NotSupportedException();

            var rented = ArrayPool<byte>.Shared.Rent(value.Length * 3);
            try
            {
                var actualByteCount = encoding.GetBytes(value, rented);

                if (value.Length <= 127 / 3)
                {
                    buf.WriteByte(actualByteCount);
                    buf.WriteBytes(rented, 0, actualByteCount);
                }
                else
                {
                    buf.Write7BitEncodedInt(actualByteCount);
                    buf.WriteBytes(rented, 0, actualByteCount);
                }
            }
            finally
            {
                ArrayPool<byte>.Shared.Return(rented);
            }
        }

        public static string ReadFixedString(this IByteBuffer buf, Encoding encoding)
        {
            var length = buf.Read7BitEncodedInt();
            if (length == 0)
                return string.Empty;

            if (length > 64 * 1024 / 3)
                throw new NotSupportedException();

            var rented = ArrayPool<byte>.Shared.Rent(length);
            try
            {
                buf.ReadBytes(rented, 0, length);
                return encoding.GetString(rented, 0, length);
            }
            finally
            {
                ArrayPool<byte>.Shared.Return(rented);
            }
        }
    }
}