using Autofac;

namespace Universe
{
    public sealed class UIModule : Module
    {
        protected override void Load(ContainerBuilder builder)
        {
            builder.RegisterType<UIWindow>().As<IUIWindow>().SingleInstance();
            builder.RegisterType<UIView>().As<IUIView>().InstancePerDependency();
            builder.RegisterType<UIWidget>().As<IUIWidget>().InstancePerDependency();
        }
    }
}