﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class NpcResponseNotify : IMessage
    {
        public const int Opcode = 91;

        public int Id => Opcode;
    }

    [Packable(Id = NpcResponseNotify.Opcode)]
    public class NpcResponseNotifyPackable : MessagePackable<NpcResponseNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out NpcResponseNotify message)
        {
            message = new NpcResponseNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}