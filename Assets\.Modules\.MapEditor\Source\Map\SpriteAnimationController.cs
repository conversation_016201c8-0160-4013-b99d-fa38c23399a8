﻿using UnityEngine;
using UnityEngine.Animations;
using UnityEngine.Playables;

namespace CLMM
{
    [RequireComponent(typeof(SpriteRenderer))]
    public class SpriteAnimationController : MonoBehaviour
    {
        public SpriteAnimationClip[] Clips => _clips;

        [SerializeField]
        private SpriteAnimationClip[] _clips;

        private SpriteRenderer _spriteRenderer;
        private PlayableGraph _graph;
        private AnimationMixerPlayable _mixer;

        private int _currentIndex;

        private void Awake()
        {
            _spriteRenderer = GetComponent<SpriteRenderer>();

            _graph = PlayableGraph.Create(gameObject.name);

            var playable = ScriptPlayable<SpritePlayableBehaviour>.Create(_graph);
            var behaviour = playable.GetBehaviour();
            behaviour.Clip = _clips[0];
            behaviour.SpriteRenderer = _spriteRenderer;

            var output = ScriptPlayableOutput.Create(_graph, "Sprite Output");
            output.SetSourcePlayable(playable);

            _graph.SetTimeUpdateMode(DirectorUpdateMode.Manual);
            _graph.Play();
        }

        private void OnDestroy()
        {
            _graph.Destroy();
        }

        public void Evaluate(float deltaTime)
        {
            _graph.Evaluate(deltaTime);
        }

        public void PlayAnimation(int index)
        {
            if (_currentIndex == index)
                return;

            _mixer.SetInputWeight(_currentIndex, 0);
            _currentIndex = index;
            _mixer.SetInputWeight(_currentIndex, 1);
        }
    }
}