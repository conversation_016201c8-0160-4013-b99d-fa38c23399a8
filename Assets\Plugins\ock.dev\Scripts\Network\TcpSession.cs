﻿using System;
using System.Threading.Tasks;
using DotNetty.Transport.Channels.Sockets;
using Newtonsoft.Json;
using slf4net;

namespace Ock
{
    public class TcpSession : TcpSocketChannel, ISession
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(TcpSession));


        private IMessageFilter _messageFilter;

        public bool IsConnected => this.Active;

        public void Initialize(IMessageFilter messageFilter)
        {
            _messageFilter = messageFilter;
        }

        public Task SendAsync(IMessage message)
        {
            if (Logger.IsInfoEnabled)
            {
                var type = message.GetType();
                if (!_messageFilter.ShouldFilter(type, LogLevel.INFO))
                    Logger.Info($"{nameof(WriteAsync)}: -I {type.Name} {JsonConvert.SerializeObject(message)}");
            }

            if (!IsConnected)
                return Task.FromException(new Exception("Session is not connected"));

            return WriteAndFlushAsync(message);
        }
    }
}