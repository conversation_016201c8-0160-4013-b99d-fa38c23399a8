﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class ClientVersionResponse : IMessage
    {
        public const int Opcode = 1;

        public int Id => Opcode;

        public byte Result;
    }

    [Packable(Id = ClientVersionResponse.Opcode)]
    public class ClientVersionResponsePackable : MessagePackable<ClientVersionResponse>
    {
        protected override void Deserialize(IByteBuffer buf, out ClientVersionResponse message)
        {
            message = new ClientVersionResponse();
            message.Result = buf.ReadByte();
        }
    }
}