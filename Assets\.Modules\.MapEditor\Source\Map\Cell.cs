﻿using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using UnityEngine.Tilemaps;

namespace CLMM
{
    public class Cell
    {
        private readonly Grid _grid;
        private readonly MapInfo _mapInfo;
        private readonly int _size;
        private readonly Dictionary<int, Sprite> _sprites;
        private readonly Dictionary<int, TileBase> _tiles;
        private readonly Transform _transform;
        private readonly int _x;
        private readonly int _y;

        public Cell(Grid grid, Dictionary<int, TileBase> tiles, Dictionary<int, Sprite> sprites, MapInfo mapInfo,
            int x, int y, int size)
        {
            _grid = grid;
            _tiles = tiles;
            _sprites = sprites;
            _mapInfo = mapInfo;
            _x = x;
            _y = y;
            _size = size;

            var go = new GameObject($"Cell ({_x}, {_y})");
            _transform = go.transform;
            _transform.parent = _grid.transform.Find("Object_Layer");

            var tileSize = _grid.cellSize;
            _transform.localPosition = new Vector3(_x * _size * tileSize.x, -_y * _size * tileSize.y, 0);
        }

        public void Load()
        {
            var tilemaps = _grid.GetComponent<TilemapContainer>().Values;
            Fill0Layer(tilemaps[0]);
            Fill1Layer(tilemaps[1]);
            Fill2Layer(tilemaps[2]);
        }

        public void Unload()
        {
            var tilemaps = _grid.GetComponent<TilemapContainer>().Values;
            Unload0Layer(tilemaps[0]);
            Unload1Layer(tilemaps[1]);
            Unload2Layer(tilemaps[2]);

            Object.DestroyImmediate(_transform.gameObject);
        }

        private void Fill0Layer(Tilemap tilemap, int scale = 2)
        {
            var size = _size / scale;
            var boundsInt = new BoundsInt(
                _x * size, -(_y + 1) * size, 0,
                size, size, 1);
            var tiles = new TileBase[size * size];

            var width = _mapInfo.Width / scale;
            var height = _mapInfo.Height / scale;
            for (var y = 0; y < size; y++)
            {
                var n = _y * size + y;
                if (n >= height)
                    continue;

                for (var x = 0; x < size; x++)
                {
                    var m = _x * size + x;
                    if (m >= width)
                        continue;

                    var tileInfo = _mapInfo.TileInfos[m * scale, n * scale];
                    if (tileInfo.BackIndex < 0)
                        continue;
                    var index = (tileInfo.BackImage & 0x1FFFFFFF) - 1;
                    if (index < 0)
                        continue;
                    var key = (tileInfo.BackIndex << 24) | (index & 0xFFFFFF);
                    _tiles.TryGetValue(key, out var tile);
                    tiles[(size - 1 - y) * size + x] = tile;
                }
            }

            tilemap.SetTilesBlock(boundsInt, tiles);
        }

        // 填充1层Tile
        private void Fill1Layer(Tilemap tilemap, int scale = 1)
        {
            var size = _size / scale;
            var boundsInt = new BoundsInt(
                _x * size, -(_y + 1) * size, 0,
                size, size, 1);
            var tiles = new TileBase[size * size];

            var width = _mapInfo.Width / scale;
            var height = _mapInfo.Height / scale;
            for (var y = 0; y < size; y++)
            {
                var n = _y * size + y;
                if (n >= height)
                    continue;

                for (var x = 0; x < size; x++)
                {
                    var m = _x * size + x;
                    if (m >= width)
                        continue;

                    var tileInfo = _mapInfo.TileInfos[m, n];
                    if (tileInfo.MiddleIndex < 0)
                        continue;
                    var index = tileInfo.MiddleImage - 1;
                    if (index < 0)
                        continue;
                    var key = (tileInfo.MiddleIndex << 24) | (index & 0xFFFFFF);
                    _tiles.TryGetValue(key, out var tile);
                    tiles[(size - 1 - y) * size + x] = tile;
                }
            }

            tilemap.SetTilesBlock(boundsInt, tiles);
        }

        private void Fill2Layer(Tilemap tilemap, int scale = 1)
        {
            var size = _size / scale;
            var boundsInt = new BoundsInt(
                _x * size, -(_y + 1) * size, 0,
                size, size, 1);
            var tiles = new TileBase[size * size];

            var width = _mapInfo.Width / scale;
            var height = _mapInfo.Height / scale;
            for (var y = 0; y < size; y++)
            {
                var n = _y * size + y;
                if (n >= height)
                    continue;

                for (var x = 0; x < size; x++)
                {
                    var m = _x * size + x;
                    if (m >= width)
                        continue;

                    var tileInfo = _mapInfo.TileInfos[m, n];
                    if (tileInfo.FrontIndex < 0)
                        continue;
                    var tileId = (tileInfo.FrontImage & 0x7FFF) - 1;
                    if (tileId < 0)
                        continue;
                    var key = (tileInfo.FrontIndex << 24) | (tileId & 0xFFFFFF);
                    _tiles.TryGetValue(key, out var tile);
                    if (tile == null)
                    {
                        if (!FillSprite(x, y, tileInfo))
                            Debug.LogWarning($"Sprite not found: atlasId={tileInfo.FrontIndex} tileId={tileId}");
                    }
                    else
                    {
                        tiles[(size - 1 - y) * size + x] = tile;
                    }
                }
            }

            tilemap.SetTilesBlock(boundsInt, tiles);
        }

        private bool FillSprite(int x, int y, TileInfo tileInfo)
        {
            var tileId = (tileInfo.FrontImage & 0x7FFF) - 1;
            var key = (tileInfo.FrontIndex << 24) | (tileId & 0xFFFFFF);

            if (!_sprites.TryGetValue(key, out var sprite))
                return false;

            var animationFrame = tileInfo.FrontAnimationFrame;
            if ((animationFrame & 0x80) > 0)
                animationFrame &= 0x7F;

            var go = new GameObject($"Sprite ({x + _x * _size}, {y + _y * _size})");
            var spriteRenderer = go.AddComponent<SpriteRenderer>();
            spriteRenderer.sortingOrder = _y * _size + y;
            spriteRenderer.sprite = sprite;
            go.hideFlags = HideFlags.DontSave;
            go.transform.parent = _transform;

            if (animationFrame > 1)
            {
                spriteRenderer.material =
                    AssetDatabase.LoadAssetAtPath<Material>("Assets/Content/Common/Material/Unlit Additive.mat");

                var spriteAnimation = go.AddComponent<SpriteAnimation>();
                var sprites = new Sprite[animationFrame - 1];
                for (var i = 0; i < sprites.Length; i++)
                {
                    key = (tileInfo.FrontIndex << 24) | ((tileId + 1 + i) & 0xFFFFFF);
                    if (_sprites.TryGetValue(key, out var frame))
                        sprites[i] = frame;
                }

                spriteAnimation.Sprites = sprites;
            }

            var tileSize = _grid.cellSize;

            go.transform.localPosition = animationFrame > 0
                ? new Vector3(x * tileSize.x + -51 / sprite.pixelsPerUnit,
                    -(y + 1) * tileSize.y + 113 / sprite.pixelsPerUnit, 0)
                : new Vector3(x * tileSize.x, -(y + 1) * tileSize.y, 0);

            return true;
        }

        private void Unload0Layer(Tilemap tilemap, int scale = 2)
        {
            var size = _size / scale;
            var boundsInt = new BoundsInt(
                _x * size, -(_y + 1) * size, 0,
                size, size, 1);
            tilemap.SetTilesBlock(boundsInt, new TileBase[size * size]);
        }

        private void Unload1Layer(Tilemap tilemap, int scale = 1)
        {
            var size = _size / scale;
            var boundsInt = new BoundsInt(
                _x * size, -(_y + 1) * size, 0,
                size, size, 1);
            tilemap.SetTilesBlock(boundsInt, new TileBase[size * size]);
        }

        private void Unload2Layer(Tilemap tilemap, int scale = 1)
        {
            var size = _size / scale;
            var boundsInt = new BoundsInt(
                _x * size, -(_y + 1) * size, 0,
                size, size, 1);
            tilemap.SetTilesBlock(boundsInt, new TileBase[size * size]);
        }
    }
}