﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEditor.Tilemaps;
using UnityEngine;
using UnityEngine.Tilemaps;

namespace CLMM.MirTools
{
    public partial class MirExport
    {
        [MenuItem("MitTools/Export All Palettes")]
        private static void GenerateAllPalettes()
        {
            EditorUtility.DisplayProgressBar("Generating All Palettes", "Processing ...", 0);

            try
            {
                var guids = AssetDatabase.FindAssets("t:TileAtlas",
                    new[] { "Assets/Content/Map/Atlas/WemadeMir2/Objects2" });

                var index = 0;
                var count = guids.Length;
                foreach (var guid in guids)
                {
                    var tileAtlasPath = AssetDatabase.GUIDToAssetPath(guid);
                    var assetPath = tileAtlasPath.Replace("Atlas", "Palette")
                        .Replace(".tileatlas.asset", ".prefab");

                    if (AssetDatabase.LoadAssetAtPath<GameObject>(assetPath) != null)
                        continue;

                    EditorUtility.DisplayProgressBar(
                        $"Generating TileAtlas({index}/{count})",
                        "Processing " + assetPath,
                        (float)++index / count);

                    var tileAtlas = AssetDatabase.LoadAssetAtPath<TileAtlas>(tileAtlasPath);
                    GeneratePalette(assetPath, tileAtlas.Tiles.Values.ToArray());
                }
            }
            finally
            {
                EditorUtility.ClearProgressBar();
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
            }
        }

        private static void GeneratePalette(string assetPath, TileBase[] tileArray)
        {
            var name = Path.GetFileNameWithoutExtension(assetPath);
            var folderPath = Path.GetDirectoryName(assetPath);
            if (!Directory.Exists(folderPath))
                Directory.CreateDirectory(folderPath);

            var tileSize = new Rect();
            foreach (var tile in tileArray)
            {
                var rect = ((Tile)tile).sprite.rect;
                if ((int)rect.width / 3 == (int)rect.height / 2)
                {
                    tileSize.x = rect.width;
                    tileSize.y = rect.height;
                    break;
                }
            }

            var cellSize = new Vector3(
                Mathf.Round(tileSize.x / 16),
                Mathf.Round(tileSize.y / 16));

            var go = GridPaletteUtility.CreateNewPalette(
                folderPath,
                name,
                GridLayout.CellLayout.Rectangle,
                GridPalette.CellSizing.Automatic,
                cellSize,
                GridLayout.CellSwizzle.XYZ);

            var tilemap = go.GetComponentInChildren<Tilemap>();
            if (tilemap == null)
            {
                Debug.LogError("Tilemap not found.");
                return;
            }

            var tiles = new List<TileBase>();
            foreach (var tile in tileArray)
            {
                var rect = ((Tile)tile).sprite.rect;
                if (Math.Abs(rect.width - tileSize.x) < 0.001f && Math.Abs(rect.height - tileSize.y) < 0.001f)
                    tiles.Add(tile);
            }

            var size = Mathf.CeilToInt(Mathf.Sqrt(tiles.Count));
            if (size % 2 != 0)
                size += 1;

            tileArray = tiles.ToArray();
            Array.Resize(ref tileArray, size * size);

            tilemap.SetTilesBlock(new BoundsInt(-(size / 2), -(size / 2), 0,
                size, size, 1), tileArray);
        }

        [MenuItem("MitTools/Export All TileAtlases")]
        private static void GenerateTileAtlas()
        {
            GenerateTileAtlas("Map/Atlas");
        }

        private static void GenerateTileAtlas(string path)
        {
            EditorUtility.DisplayProgressBar("Generating TileAtlas", "Processing ...", 0);

            try
            {
                var guids = AssetDatabase.FindAssets("t:RefSpriteAtlas",
                    new[] { $"Assets/Content/{path}" });

                var index = 0;
                var count = guids.Length;
                foreach (var guid in guids)
                {
                    var refAtlasPath = AssetDatabase.GUIDToAssetPath(guid);
                    var tileAtlasPath = refAtlasPath.Replace(".refspriteatlas.asset", ".tileatlas.asset");
                    if (AssetDatabase.LoadAssetAtPath<TileAtlas>(tileAtlasPath) != null)
                        continue;

                    EditorUtility.DisplayProgressBar(
                        $"Generating TileAtlas({index}/{count})",
                        "Processing " + tileAtlasPath,
                        (float)++index / count);

                    var refSpriteAtlas = AssetDatabase.LoadAssetAtPath<RefSpriteAtlas>(refAtlasPath);

                    var tileAtlas = ScriptableObject.CreateInstance<TileAtlas>();
                    AssetDatabase.CreateAsset(tileAtlas, tileAtlasPath);

                    foreach (var sprite in refSpriteAtlas.Sprites.Values)
                    {
                        var tile = (Tile)TileUtility.DefaultTile(sprite);
                        if (tile == null)
                            continue;
                        tile.colliderType = Tile.ColliderType.None;

                        AssetDatabase.AddObjectToAsset(tile, tileAtlas);
                        tileAtlas.Tiles.Add(int.Parse(tile.name), tile);
                    }

                    tileAtlas.SpriteAtlas = refSpriteAtlas.Atlas;
                }
            }
            finally
            {
                EditorUtility.ClearProgressBar();
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
            }
        }
    }
}