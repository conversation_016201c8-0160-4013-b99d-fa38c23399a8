﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class ObjectHeroNotify : IMessage
    {
        public const int Opcode = 25;

        public int Id => Opcode;
    }

    [Packable(Id = ObjectHeroNotify.Opcode)]
    public class ObjectHeroNotifyPackable : MessagePackable<ObjectHeroNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out ObjectHeroNotify message)
        {
            message = new ObjectHeroNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}