namespace Ock
{
    public static class EventExtensions
    {
        public static void RegisterHandlers<TEventDispatcher, TEventHandler>(this IContext context, int mask) where TEventDispatcher : IEventDispatcher where TEventHandler : IEventHandler
        {
            var handlers = context.Resolve<HandlerCollector>().Collect<TEventHandler>(mask);
            var dispatcher = context.Resolve<TEventDispatcher>();
            foreach (var (instance, attribute) in handlers)
                dispatcher.Register(attribute.Id, instance);
        }
    }
}