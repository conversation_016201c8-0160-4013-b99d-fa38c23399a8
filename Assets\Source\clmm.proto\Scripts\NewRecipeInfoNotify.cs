﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    [Protocol(LogLevel.DEBUG)]
    public class NewRecipeInfoNotify : IMessage
    {
        public const int Opcode = 264;

        public int Id => Opcode;
    }

    [Packable(Id = NewRecipeInfoNotify.Opcode)]
    public class NewRecipeInfoNotifyPackable : MessagePackable<NewRecipeInfoNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out NewRecipeInfoNotify message)
        {
            message = new NewRecipeInfoNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}