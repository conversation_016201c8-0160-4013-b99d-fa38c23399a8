﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class ChangeAttackModeNotify : IMessage
    {
        public const int Opcode = 60;

        public int Id => Opcode;
    }

    [Packable(Id = ChangeAttackModeNotify.Opcode)]
    public class ChangeAttackModeNotifyPackable : MessagePackable<ChangeAttackModeNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out ChangeAttackModeNotify message)
        {
            message = new ChangeAttackModeNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}