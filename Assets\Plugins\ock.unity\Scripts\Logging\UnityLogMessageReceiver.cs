using System.Threading;
using slf4net;
using UnityEngine;
using ILogger = slf4net.ILogger;

namespace Ock
{
    public static class UnityLogMessageReceiver
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger("Unity");

        private static void OnLogMessageReceived(string condition, string trace, LogType type)
        {
            switch (type)
            {
                case LogType.Log:
                    Logger.Info(condition + "\n" + trace);
                    break;
                case LogType.Warning:
                    Logger.Warn(condition + "\n" + trace);
                    break;
                case LogType.Error:
                case LogType.Assert:
                case LogType.Exception:
                default:
                    Logger.Error(condition + "\n" + trace);
                    break;
            }
        }

        public static void Initialize(SynchronizationContext context)
        {
            Application.logMessageReceivedThreaded += (condition, trace, type) =>
            {
#if UNITY_EDITOR
                if (condition.StartsWith("♪"))
                    return;
#endif
                context.Send(_ => OnLogMessageReceived(condition, trace, type), null);
            };
        }
    }
}