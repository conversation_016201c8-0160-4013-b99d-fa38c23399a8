﻿#if UNITY_EDITOR

using NLog;
using NLog.Config;
using NLog.Targets;
using UnityEngine;

namespace Ock
{
    [Target("UnityConsole")]
    public class UnityConsoleTarget : TargetWithLayoutHeaderAndFooter
    {
        protected override void Write(LogEventInfo logEvent)
        {
            var logMessage = "♪" + this.Layout.Render(logEvent);

            if (logEvent.Level == LogLevel.Debug
                || logEvent.Level == LogLevel.Info
                || logEvent.Level == LogLevel.Trace)
                Debug.Log(logMessage);
            else if (logEvent.Level == LogLevel.Warn)
                Debug.LogWarning(logMessage);
            else if (logEvent.Level == LogLevel.Error
                     || logEvent.Level == LogLevel.Fatal)
                Debug.LogError(logMessage);
        }

        public static void Initialize()
        {
            ConfigurationItemFactory.Default.TargetFactory.RegisterType<UnityConsoleTarget>("UnityConsole");
        }
    }
}

#endif // UNITY_EDITOR