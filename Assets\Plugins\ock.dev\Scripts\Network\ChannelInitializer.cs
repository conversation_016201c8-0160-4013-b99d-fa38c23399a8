﻿using DotNetty.Buffers;
using DotNetty.Codecs;
using DotNetty.Handlers.Logging;
using DotNetty.Handlers.Timeout;
using DotNetty.Transport.Channels;

namespace Ock
{
    public class ChannelInitializer : ChannelInitializer<TcpSession>
    {
        private readonly IMessageQueue _messageQueue;
        private readonly IMessageFilter _messageFilter;
        private readonly IProtocolSerializer _protocolSerializer;

        public ChannelInitializer(IContext context)
        {
            _messageQueue = context.Resolve<IMessageQueue>();
            _messageFilter = context.Resolve<IMessageFilter>();
            _protocolSerializer = context.Resolve<IProtocolSerializer>();
        }

        protected override void InitChannel(TcpSession channel)
        {
            channel.Initialize(_messageFilter);
            channel.Pipeline
                .AddLast(new LoggingHandler())
                .AddLast("Timeout", new IdleStateHandler(0, 5, 0))
                .AddLast(new LengthFieldBasedFrameDecoder(ByteOrder.<PERSON><PERSON><PERSON><PERSON>, 64 * 1024, 0, 2, -2, 2, true))
                .AddLast(new ProtocolEncoder(_protocolSerializer))
                .AddLast(new ProtocolDecoder(_protocolSerializer))
                .AddLast(new SessionHandler(_messageQueue, _messageFilter));
        }
    }
}