using System;
using System.Threading.Tasks;
using DotNetty.Handlers.Timeout;
using DotNetty.Transport.Channels;
using slf4net;
using Unity.Plastic.Newtonsoft.Json;

namespace Universe
{
    public class SessionHandler : ChannelHandlerAdapter
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(SessionHandler));

        private readonly IMessageDispatcher _dispatcher;
        private readonly IMessageFilter _filter;

        public SessionHandler(IMessageDispatcher dispatcher, IMessageFilter filter)
        {
            _dispatcher = dispatcher;
            _filter = filter;
        }

        public override void ChannelInactive(IChannelHandlerContext context)
        {
            if (Logger.IsInfoEnabled)
                Logger.Info($"{nameof(ChannelInactive)} {context.Channel.RemoteAddress}");

            base.ChannelInactive(context);
        }

        public override void ChannelRead(IChannelHandlerContext context, object message)
        {
            try
            {
                if (Logger.IsInfoEnabled)
                {
                    var type = message.GetType();
                    var logLevel = _filter.GetLogLevel(type);
                    if (logLevel == LogLevel.None && logLevel <= LogLevel.INFO)
                        Logger.Info($"{nameof(ChannelRead)}: -I {type.Name} {JsonConvert.SerializeObject(message)}");
                }

                _dispatcher.Dispatch((ISession)context.Channel, (IMessage)message);
            }
            catch (Exception e)
            {
                if (Logger.IsErrorEnabled)
                    Logger.Error($"{nameof(ChannelRead)}: Error dispatch packet.\r\nException - {e}");
            }
        }

        public override Task WriteAsync(IChannelHandlerContext context, object message)
        {
            if (Logger.IsInfoEnabled)
            {
                var type = message.GetType();
                var logLevel = _filter.GetLogLevel(type);
                if (logLevel == LogLevel.None && logLevel <= LogLevel.INFO)
                    Logger.Info($"{nameof(WriteAsync)}: -I {type.Name} {JsonConvert.SerializeObject(message)}");
            }

            return context.WriteAsync(message);
        }

        public override void ExceptionCaught(IChannelHandlerContext context, Exception exception)
        {
            if (Logger.IsInfoEnabled)
                Logger.Info($"{nameof(ExceptionCaught)}: Closing channel.\r\nException - {exception}");
        }

        public override void UserEventTriggered(IChannelHandlerContext context, object evt)
        {
            if (evt is IdleStateEvent)
            {
                var e = (IdleStateEvent)evt;
                if (e.State == IdleState.WriterIdle)
                    _dispatcher.Dispatch((ISession)context.Channel, new WriterIdleNotify());
            }

            base.UserEventTriggered(context, evt);
        }
    }
}