﻿using System;
using DotNetty.Buffers;

namespace Ock
{
    public interface IMessagePackable
    {
        void Serialize(IByteBuffer buf, IMessage message);
        IMessage Deserialize(IByteBuffer buf);
    }

    public abstract class MessagePackable<T> : IMessagePackable where T : IMessage
    {
        protected virtual void Serialize(IByteBuffer buf, T message)
        {
            throw new NotSupportedException();
        }

        public void Serialize(IByteBuffer buf, IMessage message)
        {
            Serialize(buf, (T)message);
        }

        protected virtual void Deserialize(IByteBuffer buf, out T message)
        {
            throw new NotSupportedException();
        }

        public IMessage Deserialize(IByteBuffer buf)
        {
            Deserialize(buf, out var message);
            return message;
        }
    }
}