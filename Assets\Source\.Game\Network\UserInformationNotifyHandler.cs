using CLMM.Constant;
using CLMM.Proto;
using CLMM.Repository;
using slf4net;
using UnityEngine;
using Universe;
using ILogger = slf4net.ILogger;
using JsonConvert = Newtonsoft.Json.JsonConvert;

namespace CLMM.Network
{
    [Component]
    [Handler((int)MessageId.UserInformationNotify, Tag = (int)GroupId.World)]
    public class UserInformationNotifyHandler : MessageHandler<UserInformationNotify>
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(UserInformationNotifyHandler));

        private readonly UserRepository _userRepository;

        public UserInformationNotifyHandler(UserRepository userRepository)
        {
            _userRepository = userRepository;
        }

        protected override void Handle(ISession session, UserInformationNotify message)
        {
            if (Logger.IsDebugEnabled)
                Logger.Debug($"{nameof(Handle)}: -I {message.GetType().Name} {JsonConvert.SerializeObject(message)}");

            World.Camera = GameObject.FindWithTag("MainCamera").GetComponent<Camera>();
            var target = new Actor();
            target.Transform = new GameObject("Actor").transform;
            target.Location = new Vector2Int(message.LocationX, message.LocationY);

            World.PlayerController = new PlayerController();
            World.PlayerController.Target = target;

            World.Camera.GetComponent<FollowCamera>().Target = target;
        }
    }
}