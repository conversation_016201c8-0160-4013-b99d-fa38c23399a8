﻿using System.Security.Cryptography;
using System.Text;
using UnityEngine;

namespace CLMM
{
    public static class ClientVersion
    {
        private static byte[] _hash;

        public static byte[] Hash
        {
            get
            {
                if (_hash == null)
                    _hash = GenerateHash();
                return _hash;
            }
        }

        private static byte[] GenerateHash()
        {
            using var md5 = MD5.Create();
            var buffer = Encoding.UTF8.GetBytes(Application.buildGUID);
            return md5.ComputeHash(buffer);
        }
    }
}