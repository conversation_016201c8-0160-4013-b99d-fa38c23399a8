﻿using System.Text;
using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class LoginRequest : IMessage
    {
        public const int Opcode = 5;

        public int Id => Opcode;

        public string Account;
        public string Password;
    }

    [Packable(Id = LoginRequest.Opcode, Requested = true)]
    public class LoginRequestPackable : MessagePackable<LoginRequest>
    {
        protected override void Serialize(IByteBuffer buf, LoginRequest message)
        {
            buf.WriteFixedString(message.Account, Encoding.UTF8);
            buf.WriteFixedString(message.Password, Encoding.UTF8);
        }
    }
}