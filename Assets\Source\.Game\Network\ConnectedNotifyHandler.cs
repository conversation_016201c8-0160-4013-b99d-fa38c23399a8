using CLMM.Constant;
using CLMM.Proto;
using CLMM.Repository;
using slf4net;
using Universe;
using ILogger = slf4net.ILogger;
using JsonConvert = Newtonsoft.Json.JsonConvert;

namespace CLMM.Network
{
    [Component]
    [Handler((int)MessageId.ConnectedNotify, Tag = (int)GroupId.Global)]
    public class ConnectedNotifyHandler : MessageHandler<ConnectedNotify>
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(ConnectedNotifyHandler));

        private readonly UserRepository _userRepository;

        public ConnectedNotifyHandler(UserRepository userRepository)
        {
            _userRepository = userRepository;
        }

        protected override void Handle(ISession session, ConnectedNotify message)
        {
            if (Logger.IsDebugEnabled)
                Logger.Debug($"{nameof(Handle)}: -I {nameof(ConnectedNotify)} {JsonConvert.SerializeObject(message)}");

            _userRepository.OnConnected(session);
        }
    }
}