﻿using System;
using System.Diagnostics;
using System.Reflection;
using System.Runtime.CompilerServices;

namespace Universe
{
    internal static class StackTraceUsageUtils
    {
        private static readonly Assembly LogAssembly = typeof(StackTraceUsageUtils).Assembly;
        private static readonly Assembly MscorlibAssembly = typeof(string).Assembly;
        private static readonly Assembly SystemAssembly = typeof(Debug).Assembly;

        private static string GetStackFrameMethodClassName(
            MethodBase method,
            bool includeNameSpace,
            bool cleanAsyncMoveNext,
            bool cleanAnonymousDelegates)
        {
            if (method == null)
                return null;
            var declaringType = method.DeclaringType;
            if (cleanAsyncMoveNext && method.Name == "MoveNext" && declaringType?.DeclaringType != null &&
                declaringType.Name.StartsWith("<") && declaringType.Name.IndexOf('>', 1) > 1)
                declaringType = declaringType.DeclaringType;
            if (!includeNameSpace && declaringType?.DeclaringType != null && declaringType.IsNested &&
                declaringType.GetCustomAttribute<CompilerGeneratedAttribute>() != null)
                return declaringType.DeclaringType.Name;
            var frameMethodClassName = includeNameSpace ? declaringType?.FullName : declaringType?.Name;
            if (cleanAnonymousDelegates && frameMethodClassName != null)
            {
                var length = frameMethodClassName.IndexOf("+<>", StringComparison.Ordinal);
                if (length >= 0)
                    frameMethodClassName = frameMethodClassName.Substring(0, length);
            }

            return frameMethodClassName;
        }

        /// <summary>
        ///     Gets the fully qualified name of the class invoking the calling method, including the
        ///     namespace but not the assembly.
        /// </summary>
        [MethodImpl(MethodImplOptions.NoInlining)]
        public static string GetClassFullName()
        {
            return GetClassFullName(new StackFrame(2, false));
        }

        /// <summary>
        ///     Gets the fully qualified name of the class invoking the calling method, including the
        ///     namespace but not the assembly.
        /// </summary>
        /// <param name="stackFrame">StackFrame from the calling method</param>
        /// <returns>Fully qualified class name</returns>
        private static string GetClassFullName(StackFrame stackFrame)
        {
            var classFullName = LookupClassNameFromStackFrame(stackFrame);
            if (!string.IsNullOrEmpty(classFullName))
                return classFullName;

            classFullName = GetClassFullName(new StackTrace(false));
            if (string.IsNullOrEmpty(classFullName))
                classFullName = stackFrame.GetMethod()?.Name ?? string.Empty;

            return classFullName;
        }

        private static string GetClassFullName(StackTrace stackTrace)
        {
            foreach (var frame in stackTrace.GetFrames())
            {
                var classFullName = LookupClassNameFromStackFrame(frame);
                if (!string.IsNullOrEmpty(classFullName))
                    return classFullName;
            }

            return string.Empty;
        }

        /// <summary>
        ///     Returns the assembly from the provided StackFrame (If not internal assembly)
        /// </summary>
        /// <returns>Valid assembly, or null if assembly was internal</returns>
        private static Assembly LookupAssemblyFromStackFrame(StackFrame stackFrame)
        {
            var method = stackFrame.GetMethod();
            if (method == null)
                return null;

            var declaringType = method.DeclaringType;
            var assembly = declaringType != null
                ? declaringType.Assembly
                : method.Module.Assembly;

            if (assembly == LogAssembly
                || assembly == MscorlibAssembly
                || assembly == SystemAssembly)
                return null;

            return assembly;
        }

        /// <summary>
        ///     Returns the classname from the provided StackFrame (If not from internal assembly)
        /// </summary>
        /// <param name="stackFrame"></param>
        /// <returns>Valid class name, or empty string if assembly was internal</returns>
        private static string LookupClassNameFromStackFrame(StackFrame stackFrame)
        {
            var method = stackFrame.GetMethod();
            if (method == null || LookupAssemblyFromStackFrame(stackFrame) == null)
                return string.Empty;

            var frameMethodClassName = GetStackFrameMethodClassName(method, true, true, true);
            if (!string.IsNullOrEmpty(frameMethodClassName))
            {
                if (!frameMethodClassName.StartsWith("System.", StringComparison.Ordinal))
                    return frameMethodClassName;
            }
            else
            {
                var methodName = method.Name;
                if (methodName != "lambda_method" && methodName != "MoveNext")
                    return methodName;
            }

            return string.Empty;
        }
    }
}