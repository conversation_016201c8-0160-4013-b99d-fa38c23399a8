using System;
using System.Collections.Generic;

namespace Universe
{
    public class MessageFilter : IMessageFilter
    {
        private readonly Dictionary<Type, LogLevel> _filters = new();

        public LogLevel GetLogLevel(Type type, LogLevel @default = LogLevel.INFO)
        {
            return _filters.TryGetValue(type, out var logLevel) ? logLevel : @default;
        }

        public void Add(Type type, LogLevel logLevel)
        {
            _filters[type] = logLevel;
        }

        public bool Remove(Type type)
        {
            return _filters.Remove(type);
        }

        public void Clear()
        {
            _filters.Clear();
        }
    }
}