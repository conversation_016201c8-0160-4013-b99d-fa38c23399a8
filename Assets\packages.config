﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Autofac" version="8.2.0" manuallyInstalled="true" />
  <package id="Autofac.Annotation" version="4.4.5" manuallyInstalled="true" />
  <package id="Castle.Core" version="5.1.1" />
  <package id="DotNetty.Buffers" version="0.7.6" />
  <package id="DotNetty.Codecs" version="0.7.6" />
  <package id="DotNetty.Common" version="0.7.6" />
  <package id="DotNetty.Handlers" version="0.7.6" manuallyInstalled="true" />
  <package id="DotNetty.Transport" version="0.7.6" />
  <package id="MemoryPack" version="1.21.3" manuallyInstalled="true" />
  <package id="MemoryPack.Core" version="1.21.3" />
  <package id="MemoryPack.Generator" version="1.21.3" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="6.0.0" />
  <package id="Microsoft.Bcl.TimeProvider" version="8.0.0" />
  <package id="Microsoft.Extensions.Configuration" version="3.1.0" />
  <package id="Microsoft.Extensions.Configuration.Abstractions" version="3.1.0" />
  <package id="Microsoft.Extensions.Configuration.Binder" version="3.1.0" />
  <package id="Microsoft.Extensions.Configuration.FileExtensions" version="3.1.0" />
  <package id="Microsoft.Extensions.Configuration.Json" version="3.1.0" />
  <package id="Microsoft.Extensions.Configuration.Xml" version="3.1.0" />
  <package id="Microsoft.Extensions.DependencyInjection" version="5.0.0" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="5.0.0" />
  <package id="Microsoft.Extensions.FileProviders.Abstractions" version="3.1.0" />
  <package id="Microsoft.Extensions.FileProviders.Physical" version="3.1.0" />
  <package id="Microsoft.Extensions.FileSystemGlobbing" version="3.1.0" />
  <package id="Microsoft.Extensions.Logging" version="5.0.0" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="5.0.0" />
  <package id="Microsoft.Extensions.Options" version="5.0.0" />
  <package id="Microsoft.Extensions.Primitives" version="5.0.0" />
  <package id="MongoDB.Bson" version="3.1.0" manuallyInstalled="true" />
  <package id="NLog" version="5.4.0" />
  <package id="NLog.Extensions.Logging" version="5.4.0" manuallyInstalled="true" />
  <package id="Newtonsoft.Json" version="13.0.3" manuallyInstalled="true" />
  <package id="R3" version="1.3.0" manuallyInstalled="true" />
  <package id="Spring.EL" version="1.0.5" />
  <package id="System.CodeDom" version="4.5.0" />
  <package id="System.Collections.Immutable" version="6.0.0" />
  <package id="System.Configuration.ConfigurationManager" version="4.7.0" />
  <package id="System.Diagnostics.DiagnosticSource" version="8.0.1" />
  <package id="System.Diagnostics.EventLog" version="4.7.0" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" />
  <package id="System.Security.AccessControl" version="4.7.0" />
  <package id="System.Security.Cryptography.Cng" version="4.7.0" />
  <package id="System.Security.Cryptography.Pkcs" version="4.7.0" />
  <package id="System.Security.Cryptography.ProtectedData" version="4.7.0" />
  <package id="System.Security.Cryptography.Xml" version="4.7.0" />
  <package id="System.Security.Permissions" version="4.7.0" />
  <package id="System.Security.Principal.Windows" version="4.7.0" />
  <package id="System.Text.Encodings.Web" version="4.7.0" />
  <package id="System.Text.Json" version="4.7.0" />
  <package id="System.Threading.Channels" version="8.0.0" />
  <package id="slf4net" version="1.1.0" manuallyInstalled="true" />
  <package id="slf4net.NLog" version="1.1.0" manuallyInstalled="true" />
</packages>