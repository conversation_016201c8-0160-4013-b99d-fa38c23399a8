%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 65bae8b9f1bd244b3a27e92af4b23b2a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _data:
    _json: '{"dictionary":{"aotSafeMode":{"$content":true,"$type":"System.Boolean"},"favoriteMembers":{"$content":[],"$type":"System.Collections.Generic.HashSet`1[[Unity.VisualScripting.Member,
      Unity.VisualScripting.Core, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]"},"LinkerPropertyProviderSettings":{"$content":[true,true,true],"$type":"System.Collections.Generic.List`1[[System.Boolean,
      mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]"},"assemblyOptions":{"$content":["mscorlib","Assembly-CSharp-firstpass","Assembly-CSharp","UnityEngine","UnityEngine.CoreModule","UnityEngine.InputModule","UnityEngine.ClusterInputModule","UnityEngine.InputLegacyModule","UnityEngine.PhysicsModule","UnityEngine.Physics2DModule","UnityEngine.TerrainPhysicsModule","UnityEngine.VehiclesModule","UnityEngine.AudioModule","UnityEngine.AnimationModule","UnityEngine.VideoModule","UnityEngine.DirectorModule","UnityEngine.Timeline","UnityEngine.ParticleSystemModule","UnityEngine.ParticlesLegacyModule","UnityEngine.WindModule","UnityEngine.ClothModule","UnityEngine.TilemapModule","UnityEngine.SpriteMaskModule","UnityEngine.TerrainModule","UnityEngine.ImageConversionModule","UnityEngine.TextRenderingModule","UnityEngine.ClusterRendererModule","UnityEngine.ScreenCaptureModule","UnityEngine.AIModule","UnityEngine.UI","UnityEngine.UIModule","UnityEngine.IMGUIModule","UnityEngine.UIElementsModule","UnityEngine.StyleSheetsModule","UnityEngine.VR","UnityEngine.VRModule","UnityEngine.ARModule","UnityEngine.HoloLens","UnityEngine.SpatialTracking","UnityEngine.GoogleAudioSpatializer","UnityEngine.Networking","UnityEngine.Analytics","UnityEngine.Advertisements","UnityEngine.Purchasing","UnityEngine.UnityConnectModule","UnityEngine.UnityAnalyticsModule","UnityEngine.GameCenterModule","UnityEngine.AccessibilityModule","UnityEngine.AndroidJNIModule","UnityEngine.AssetBundleModule","UnityEngine.FileSystemHttpModule","UnityEngine.JSONSerializeModule","UnityEngine.UmbraModule","Unity.Timeline","Unity.Timeline.Editor","Cinemachine","com.unity.cinemachine.editor","Unity.InputSystem","Unity.TextMeshPro","Unity.VisualScripting.Core","Unity.VisualScripting.Flow","Unity.VisualScripting.State"],"$type":"System.Collections.Generic.List`1[[Unity.VisualScripting.LooseAssemblyName,
      Unity.VisualScripting.Core, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]"},"typeOptions":{"$content":["System.Object","System.Boolean","System.Int32","System.Single","System.String","UnityEngine.Vector2","UnityEngine.Vector3","UnityEngine.Vector4","UnityEngine.Quaternion","UnityEngine.Matrix4x4","UnityEngine.Rect","UnityEngine.Bounds","UnityEngine.Color","UnityEngine.AnimationCurve","UnityEngine.LayerMask","UnityEngine.Ray","UnityEngine.Ray2D","UnityEngine.RaycastHit","UnityEngine.RaycastHit2D","UnityEngine.ContactPoint","UnityEngine.ContactPoint2D","UnityEngine.ParticleCollisionEvent","UnityEngine.SceneManagement.Scene","UnityEngine.Application","UnityEngine.Resources","UnityEngine.Mathf","UnityEngine.Debug","UnityEngine.Input","UnityEngine.Touch","UnityEngine.Screen","UnityEngine.Cursor","UnityEngine.Time","UnityEngine.Random","UnityEngine.Physics","UnityEngine.Physics2D","UnityEngine.SceneManagement.SceneManager","UnityEngine.GUI","UnityEngine.GUILayout","UnityEngine.GUIUtility","UnityEngine.Audio.AudioMixerGroup","UnityEngine.AI.NavMesh","UnityEngine.Gizmos","UnityEngine.AnimatorStateInfo","UnityEngine.EventSystems.BaseEventData","UnityEngine.EventSystems.PointerEventData","UnityEngine.EventSystems.AxisEventData","System.Collections.IList","System.Collections.IDictionary","Unity.VisualScripting.AotList","Unity.VisualScripting.AotDictionary","System.Exception"],"$type":"System.Collections.Generic.List`1[[System.Type,
      mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]"},"projectSetupCompleted":{"$content":false,"$type":"System.Boolean"},"savedVersion":{"major":1,"minor":9,"patch":8,"label":null,"increment":0,"$type":"Unity.VisualScripting.SemanticVersion"}}}'
    _objectReferences: []
