﻿using System;
using System.IO;
using System.Threading;
using Cysharp.Threading.Tasks;
using Ock;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.SceneManagement;
using ILogger = slf4net.ILogger;
using Object = UnityEngine.Object;

namespace CLMM
{
    internal class Application
    {
        private static ILogger Logger;

        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.AfterSceneLoad)]
        private static void Run()
        {
#if UNITY_EDITOR
            var scene = SceneManager.GetActiveScene();
            if (scene.name != "Game")
                return;
#endif
            SetupExceptionHandler();
            InitializeLogging();
            RunAsync().Forget();
        }

        private async static UniTaskVoid RunAsync()
        {
            await Addressables.InitializeAsync().ToUniTask();

            var go = new GameObject($"[{nameof(GameBehavior)}]");
            Object.DontDestroyOnLoad(go);
            go.AddComponent<GameBehavior>();
        }

        private static void InitializeLogging()
        {
#if UNITY_EDITOR
            UnityConsoleTarget.Initialize();
            var path = Path.Combine(UnityEngine.Application.dataPath, "Settings/Editor/NLog.xml");
            var text = File.ReadAllText(path);
            LoggerManager.LoadCfg(text);
#else
            var path = Path.Combine(UnityEngine.Application.persistentDataPath, "NLog.xml");
            var text = File.Exists(path)
                ? File.ReadAllText(path)
                : Resources.Load<TextAsset>("NLog").text;
            LoggerManager.LoadCfg(text);
#endif
            UnityLogMessageReceiver.Initialize(SynchronizationContext.Current);

            Logger ??= LoggerManager.GetLogger(typeof(Application));
        }

        private static void SetupExceptionHandler()
        {
            AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
            {
                if (Logger.IsErrorEnabled)
                    Logger.Error($"UnhandledException. -I sender: {sender}\r\nException: {e.ExceptionObject}");
            };

            // UniTask的Forget异常会发送到这里
            UniTaskScheduler.UnobservedTaskException += e =>
            {
                if (Logger.IsErrorEnabled)
                    Logger.Error($"UnobservedTaskException.\r\nException: {e}");
            };
        }
    }
}