using System;
using System.Collections.Generic;
using DotNetty.Buffers;
using DotNetty.Codecs;
using DotNetty.Transport.Channels;
using slf4net;

namespace Ock
{
    public class ProtocolDecoder : MessageToMessageDecoder<IByteBuffer>
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(ProtocolDecoder));


        private readonly IProtocolSerializer _protocolSerializer;

        public ProtocolDecoder(IProtocolSerializer protocolSerializer)
        {
            _protocolSerializer = protocolSerializer;
        }

        protected override void Decode(
            IChannelHandlerContext context,
            IByteBuffer input,
            List<object> output)
        {
            object packet = null;
            try
            {
                packet = _protocolSerializer.Deserialize(input);
                if (input.ReadableBytes > 0)
                {
                    if (Logger.IsWarnEnabled)
                        Logger.Warn($"{nameof(Decode)}: -I input.ReadableBytes: {input.ReadableBytes}");
                }
            }
            catch (Exception e)
            {
                if (Logger.IsErrorEnabled)
                    Logger.Error($"{nameof(Decode)}: Deserialize.\r\nException - {e}");
            }

            if (packet != null)
                output.Add(packet);
        }
    }
}