﻿// using System;
// using System.Collections.Generic;
// using System.Drawing;
// using System.Drawing.Imaging;
// using System.IO;
// using System.Linq;
// using System.Runtime.InteropServices;
// using MemoryPack;
// using Sirenix.OdinInspector;
// using UnityEditor;
// using UnityEditor.Tilemaps;
// using UnityEditor.U2D;
// using UnityEngine;
// using UnityEngine.Tilemaps;
// using UnityEngine.U2D;
// using Object = UnityEngine.Object;
//
// namespace CLMM.MirTools
// {
//     public class MapExport : MonoBehaviour
//     {
//         [BoxGroup("Export", Order = 0)]
//         [ValidateInput("ValidateMapAsset")]
//         [LabelText("Asset")]
//         public TextAsset MapAsset;
//
//         private static bool ValidateMapAsset(TextAsset value, ref string errorMessage)
//         {
//             if (value == null)
//                 return false;
//
//             // Check if the asset is a valid map
//             if (!value.name.Contains(".map"))
//             {
//                 errorMessage = "\"" + value.name + "\" is not a valid map file";
//                 return false;
//             }
//
//             return true;
//         }
//
//         private static int GetAtlasId(string name)
//         {
//             var atlasNames = new string[40];
//             atlasNames[0] = "Tiles";
//             atlasNames[1] = "Smtiles";
//             atlasNames[2] = "Objects";
//             for (var i = 2; i < 28; i++)
//                 atlasNames[i + 1] = "Objects" + i;
//
//             for (var i = 0; i < atlasNames.Length; i++)
//                 if (atlasNames[i].Contains(name))
//                     return i;
//
//             return -1;
//         }
//
//         [EnableIf("MapAsset")]
//         [BoxGroup("Export")]
//         [Button("Export Map Texture", ButtonSizes.Large)]
//         [GUIColor(0, 0.9f, 0)]
//         private void ExportMapTexture()
//         {
//             Debug.Log("Exporting texture from map file: " + MapAsset.name);
//
//             var mapInfo = MemoryPackSerializer.Deserialize<MirMapInfo>(MapAsset.bytes);
//             var dict = new Dictionary<int, HashSet<int>>();
//             GetLayer0Texture(mapInfo, ref dict);
//             GetLayer1Texture(mapInfo, ref dict);
//             GetLayer2Texture(mapInfo, ref dict);
//
//             try
//             {
//                 var libs = GetMapLibs("WemadeMir2");
//                 var i = 0;
//                 var count = dict.Sum(kv => kv.Value.Count);
//                 foreach (var (atlasId, spriteIds) in dict)
//                 {
//                     if (atlasId < 0 || atlasId >= libs.Length)
//                     {
//                         Debug.LogWarning($"Invalid atlasId: {atlasId}");
//                         continue;
//                     }
//
//                     var lib = libs[atlasId];
//                     if (lib == null)
//                     {
//                         Debug.LogWarning($"Invalid lib: {atlasId} textures: {string.Join(",", spriteIds)}");
//                         continue;
//                     }
//
//                     var atlasName = lib.Name;
//
//                     foreach (var spriteId in spriteIds)
//                     {
//                         EditorUtility.DisplayProgressBar("Exporting Map Texture", $"Exporting {atlasId}/{spriteId}.png",
//                             (float)++i / count);
//                         var image = lib.LoadImage(spriteId);
//                         if (image == null || image.Width < 5 || image.Height < 5 || image.Data == null)
//                             continue;
//
//                         var regular = (image.Width == 48 && image.Height == 32) ||
//                                       (image.Width == 48 * 2 && image.Height == 32 * 2);
//
//                         var path = regular
//                             ? Path.Combine(Application.dataPath,
//                                 $"Content/Map/Texture/WemadeMir2/{atlasName}/{spriteId}.png")
//                             : Path.Combine(Application.dataPath,
//                                 $"Content/Map/TextureV2/WemadeMir2/{atlasName}/{spriteId}.png");
//
//                         if (File.Exists(path))
//                             continue;
//
//                         var directory = Path.GetDirectoryName(path);
//                         if (!Directory.Exists(directory))
//                             Directory.CreateDirectory(directory);
//
//                         using var bitmap = GenerateBitmap(image.Width, image.Height, image.Data);
//                         bitmap.Save(path, ImageFormat.Png);
//                     }
//                 }
//             }
//             finally
//             {
//                 Resources.UnloadUnusedAssets();
//                 EditorUtility.ClearProgressBar();
//                 AssetDatabase.Refresh();
//             }
//         }
//
//         [BoxGroup("Generate")]
//         [Button("Generate SpriteAtlas", ButtonSizes.Large)]
//         [GUIColor(0, 0.9f, 0)]
//         private static void GenerateSpriteAtlas()
//         {
//             Debug.Log("Generating SpriteAtlas");
//             GenerateSpriteAtlas("Map/Texture/WemadeMir2");
//             GenerateSpriteAtlas("Map/TextureV2/WemadeMir2");
//         }
//
//         private static void GenerateSpriteAtlas(string path)
//         {
//             EditorUtility.DisplayProgressBar("Generating SpriteAtlas", "Processing ...", 0);
//
//             try
//             {
//                 var directories = Directory.GetDirectories($"Assets/Content/{path}");
//
//                 var index = 0;
//                 var count = directories.Length;
//                 foreach (var directory in directories)
//                 {
//                     EditorUtility.DisplayProgressBar(
//                         $"Generating SpriteAtlas({index}/{count})",
//                         "Processing " + directory,
//                         (float)++index / count);
//
//                     var atlasPath = directory.Replace("Texture", "Atlas") + ".spriteatlasv2";
//                     var atlasAsset = SpriteAtlasAsset.Load(atlasPath);
//                     if (atlasAsset != null)
//                         continue;
//                     atlasAsset = new SpriteAtlasAsset();
//                     var folder = AssetDatabase.LoadAssetAtPath(directory, typeof(Object));
//                     atlasAsset.Add(new[] { folder });
//
//                     var dirPath = Path.GetDirectoryName(atlasPath);
//                     if (!Directory.Exists(dirPath))
//                         Directory.CreateDirectory(dirPath);
//
//                     SpriteAtlasAsset.Save(atlasAsset, atlasPath);
//                 }
//             }
//             finally
//             {
//                 EditorUtility.ClearProgressBar();
//                 AssetDatabase.SaveAssets();
//                 AssetDatabase.Refresh();
//             }
//         }
//
//
//         [BoxGroup("Generate")]
//         [Button("Generate SpriteAtlasReference", ButtonSizes.Large)]
//         [GUIColor(0, 0.9f, 0)]
//         private static void GenerateSpriteAtlasReference()
//         {
//             Debug.Log("Generating SpriteAtlasReference");
//             GenerateSpriteAtlasReference("Map/Texture/WemadeMir2");
//             GenerateSpriteAtlasReference("Map/TextureV2/WemadeMir2");
//         }
//
//         private static void GenerateSpriteAtlasReference(string path)
//         {
//             EditorUtility.DisplayProgressBar("Generating SpriteAtlasReference", "Processing ...", 0);
//
//             try
//             {
//                 var directories = Directory.GetDirectories($"Assets/Content/{path}");
//
//                 var index = 0;
//                 var count = directories.Length;
//                 foreach (var directory in directories)
//                 {
//                     EditorUtility.DisplayProgressBar(
//                         $"Generating SpriteAtlasReference({index}/{count})",
//                         "Processing " + directory,
//                         (float)++index / count);
//
//                     var referencePath = directory.Replace("Texture", "Atlas") + ".spriteatlasref.asset";
//                     if (AssetDatabase.LoadAssetAtPath<SpriteAtlasReference>(referencePath) != null)
//                         continue;
//                     var spriteAtlasReference = ScriptableObject.CreateInstance<SpriteAtlasReference>();
//
//                     var atlasPath = referencePath.Replace(".spriteatlasref.asset", ".spriteatlasv2");
//                     spriteAtlasReference.Atlas = AssetDatabase.LoadAssetAtPath<SpriteAtlas>(atlasPath);
//
//                     var guids = AssetDatabase.FindAssets("t:Sprite", new[] { directory });
//                     foreach (var guid in guids)
//                     {
//                         var spritePath = AssetDatabase.GUIDToAssetPath(guid);
//                         var sprite = AssetDatabase.LoadAssetAtPath<Sprite>(spritePath);
//                         spriteAtlasReference.Sprites.Add(int.Parse(sprite.name), sprite);
//                     }
//
//                     spriteAtlasReference.Id = GetAtlasId(Path.GetFileNameWithoutExtension(directory));
//
//                     AssetDatabase.CreateAsset(spriteAtlasReference, referencePath);
//                 }
//             }
//             finally
//             {
//                 EditorUtility.ClearProgressBar();
//                 AssetDatabase.SaveAssets();
//                 AssetDatabase.Refresh();
//             }
//         }
//
//         [BoxGroup("Generate")]
//         [Button("Generate TileAtlas", ButtonSizes.Large)]
//         [GUIColor(0, 0.9f, 0)]
//         private static void GenerateTileAtlas()
//         {
//             Debug.Log("Generating TileAtlas");
//             GenerateTileAtlas("Map/Atlas/WemadeMir2");
//         }
//
//         private static void GenerateTileAtlas(string path)
//         {
//             EditorUtility.DisplayProgressBar("Generating TileAtlas", "Processing ...", 0);
//
//             try
//             {
//                 var guids = AssetDatabase.FindAssets("t:SpriteAtlasReference",
//                     new[] { $"Assets/Content/{path}" });
//
//                 var index = 0;
//                 var count = guids.Length;
//                 foreach (var guid in guids)
//                 {
//                     var referencePath = AssetDatabase.GUIDToAssetPath(guid);
//                     var atlasPath = referencePath.Replace(".spriteatlasref.asset", ".tileatlas.asset");
//                     if (AssetDatabase.LoadAssetAtPath<TileAtlas>(atlasPath) != null)
//                         continue;
//
//                     EditorUtility.DisplayProgressBar(
//                         $"Generating TileAtlas({index}/{count})",
//                         "Processing " + atlasPath,
//                         (float)++index / count);
//
//                     var spriteAtlasReference = AssetDatabase.LoadAssetAtPath<SpriteAtlasReference>(referencePath);
//
//                     var tileAtlas = ScriptableObject.CreateInstance<TileAtlas>();
//                     AssetDatabase.CreateAsset(tileAtlas, atlasPath);
//
//                     foreach (var sprite in spriteAtlasReference.Sprites.Values)
//                     {
//                         var tile = TileUtility.DefaultTile(sprite) as Tile;
//                         if (tile == null)
//                             continue;
//                         tile.colliderType = Tile.ColliderType.None;
//
//                         AssetDatabase.AddObjectToAsset(tile, tileAtlas);
//                         tileAtlas.Tiles.Add(int.Parse(tile.name), tile);
//                     }
//
//                     tileAtlas.Id = spriteAtlasReference.Id;
//                 }
//             }
//             finally
//             {
//                 EditorUtility.ClearProgressBar();
//                 AssetDatabase.SaveAssets();
//                 AssetDatabase.Refresh();
//             }
//         }
//
//         [BoxGroup("Generate")]
//         [Button("Generate Palette", ButtonSizes.Large)]
//         [GUIColor(0, 0.9f, 0)]
//         public static void GeneratePalette()
//         {
//             Debug.Log("Generating Palette");
//             EditorUtility.DisplayProgressBar("Generating Palette", "Processing ...", 0);
//
//             try
//             {
//                 var guids = AssetDatabase.FindAssets("t:TileAtlas",
//                     new[] { "Assets/Content/Map/Atlas" });
//
//                 var index = 0;
//                 var count = guids.Length;
//                 foreach (var guid in guids)
//                 {
//                     var atlasPath = AssetDatabase.GUIDToAssetPath(guid);
//                     var assetPath = atlasPath.Replace("Atlas", "Palette")
//                         .Replace(".tileatlas.asset", ".prefab");
//
//                     if (AssetDatabase.LoadAssetAtPath<GameObject>(assetPath) != null)
//                         continue;
//
//                     EditorUtility.DisplayProgressBar(
//                         $"Generating TileAtlas({index}/{count})",
//                         "Processing " + assetPath,
//                         (float)++index / count);
//
//                     var tileAtlas = AssetDatabase.LoadAssetAtPath<TileAtlas>(atlasPath);
//                     if (tileAtlas == null)
//                     {
//                         Debug.LogError($"TileAtlas not found: {atlasPath}");
//                         continue;
//                     }
//
//                     GeneratePalette(assetPath, tileAtlas.Tiles.Values.ToArray());
//                 }
//             }
//             finally
//             {
//                 EditorUtility.ClearProgressBar();
//                 AssetDatabase.SaveAssets();
//                 AssetDatabase.Refresh();
//             }
//         }
//
//         /*
//         [BoxGroup("Generate")]
//         [Button("Generate Tile", ButtonSizes.Large)]
//         [GUIColor(0, 0.9f, 0)]
//         public static void GenerateTile()
//         {
//             Debug.Log("Generating Tile");
//             EditorUtility.DisplayProgressBar("Generating Tile", "Processing ...", 0);
//
//             try
//             {
//                 var directories = Directory.GetDirectories("Assets/Content/Map/Texture/WemadeMir2");
//
//                 var index = 0;
//                 var count = directories.Length;
//                 foreach (var directory in directories)
//                 {
//                     EditorUtility.DisplayProgressBar(
//                         $"Generating Tile({index}/{count})",
//                         "Processing " + directory,
//                         (float)++index / count);
//
//                     var guids = AssetDatabase.FindAssets("t:Sprite", new[] { directory });
//                     foreach (var guid in guids)
//                     {
//                         var spritePath = AssetDatabase.GUIDToAssetPath(guid);
//                         var assetPath = spritePath.Replace("Texture", "Tile")
//                             .Replace("png", "asset");
//                         if (AssetDatabase.LoadAssetAtPath<Tile>(assetPath) != null)
//                             continue;
//
//                         var sprite = AssetDatabase.LoadAssetAtPath<Sprite>(spritePath);
//                         var tile = TileUtility.DefaultTile(sprite) as Tile;
//                         if (tile == null)
//                             continue;
//                         tile.colliderType = Tile.ColliderType.None;
//
//                         var path = Path.GetDirectoryName(assetPath);
//                         if (!Directory.Exists(path))
//                             Directory.CreateDirectory(path);
//
//                         AssetDatabase.CreateAsset(tile, assetPath);
//                     }
//                 }
//             }
//             finally
//             {
//                 EditorUtility.ClearProgressBar();
//                 AssetDatabase.SaveAssets();
//                 AssetDatabase.Refresh();
//             }
//         }
//
//         [BoxGroup("Generate")]
//         [Button("Generate Palette", ButtonSizes.Large)]
//         [GUIColor(0, 0.9f, 0)]
//         public static void GeneratePalette()
//         {
//             Debug.Log("Generating Palette");
//             EditorUtility.DisplayProgressBar("Generating Palette", "Processing ...", 0);
//
//             try
//             {
//                 var directories = Directory.GetDirectories("Assets/Content/Map/Tile/WemadeMir2");
//
//                 var index = 0;
//                 var count = directories.Length;
//                 foreach (var directory in directories)
//                 {
//                     EditorUtility.DisplayProgressBar(
//                         $"Generating Palette({index}/{count})",
//                         "Processing " + directory,
//                         (float)++index / count);
//
//                     var tiles = new List<TileBase>();
//                     var guids = AssetDatabase.FindAssets("t:TileBase", new[] { directory });
//                     foreach (var guid in guids)
//                     {
//                         var tilePath = AssetDatabase.GUIDToAssetPath(guid);
//                         var tile = AssetDatabase.LoadAssetAtPath<TileBase>(tilePath);
//                         tiles.Add(tile);
//                     }
//
//                     var assetPath = directory.Replace("/Tile/", "/Palette/");
//                     GeneratePalette(assetPath, tiles.ToArray());
//                 }
//             }
//             finally
//             {
//                 EditorUtility.ClearProgressBar();
//                 AssetDatabase.SaveAssets();
//                 AssetDatabase.Refresh();
//             }
//         }
//         */
//
//         private static void GeneratePalette(string assetPath, TileBase[] tileArray)
//         {
//             var name = Path.GetFileNameWithoutExtension(assetPath);
//             var folderPath = Path.GetDirectoryName(assetPath);
//             if (!Directory.Exists(folderPath))
//                 Directory.CreateDirectory(folderPath);
//
//             var go = GridPaletteUtility.CreateNewPalette(
//                 folderPath,
//                 name,
//                 GridLayout.CellLayout.Rectangle,
//                 GridPalette.CellSizing.Automatic,
//                 new Vector3(3, 2, 0),
//                 GridLayout.CellSwizzle.XYZ);
//
//             var tilemap = go.GetComponentInChildren<Tilemap>();
//             if (tilemap == null)
//             {
//                 Debug.LogError("Tilemap not found.");
//                 return;
//             }
//
//             var size = Mathf.CeilToInt(Mathf.Sqrt(tileArray.Length));
//             if (size % 2 != 0)
//                 size += 1;
//
//             Array.Resize(ref tileArray, size * size);
//
//             tilemap.SetTilesBlock(new BoundsInt(-(size / 2), -(size / 2), 0,
//                 size, size, 1), tileArray);
//         }
//
//         private static Bitmap GenerateBitmap(int width, int height, byte[] source)
//         {
//             var bitmap = new Bitmap(width, height);
//             var data = bitmap.LockBits(new Rectangle(0, 0, width, height), ImageLockMode.ReadWrite,
//                 PixelFormat.Format32bppArgb);
//             Marshal.Copy(source, 0, data.Scan0, source.Length);
//             bitmap.UnlockBits(data);
//             return bitmap;
//         }
//
//         private static MirLibV2[] GetMapLibs(string path)
//         {
//             var result = new MirLibV2[400];
//             var dataPath = Application.dataPath.Replace("/Assets", "/Data/Content/Map");
//             result[0] = new MirLibV2(Path.Combine(dataPath, $"{path}/Tiles.lib"));
//             result[1] = new MirLibV2(Path.Combine(dataPath, $"{path}/Smtiles.lib"));
//             result[2] = new MirLibV2(Path.Combine(dataPath, $"{path}/Objects.lib"));
//             for (var i = 2; i < 28; i++)
//                 result[i + 1] = new MirLibV2(Path.Combine(dataPath, $"{path}/Objects{i}.lib"));
//             return result;
//         }
//
//         private static void GetLayer0Texture(MirMapInfo mapInfo, ref Dictionary<int, HashSet<int>> dict)
//         {
//             for (var y = 0; y < mapInfo.Height; y++)
//             {
//                 if (y % 2 == 1)
//                     continue;
//                 for (var x = 0; x < mapInfo.Width; x++)
//                 {
//                     if (x % 2 == 1)
//                         continue;
//                     var tileInfo = mapInfo.TileInfos[x, y];
//                     var backIndex = tileInfo.BackIndex;
//                     var image = (tileInfo.BackImage & 0x1FFFFFFF) - 1;
//                     if (image < 0)
//                         continue;
//                     if (!dict.TryGetValue(backIndex, out var imageSet))
//                     {
//                         imageSet = new HashSet<int>();
//                         dict.Add(backIndex, imageSet);
//                     }
//
//                     imageSet.Add(image);
//                 }
//             }
//         }
//
//         private static void GetLayer1Texture(MirMapInfo mapInfo, ref Dictionary<int, HashSet<int>> dict)
//         {
//             for (var y = 0; y < mapInfo.Height; y++)
//             for (var x = 0; x < mapInfo.Width; x++)
//             {
//                 var tileInfo = mapInfo.TileInfos[x, y];
//                 var middleIndex = tileInfo.MiddleIndex;
//                 var image = tileInfo.MiddleImage - 1;
//                 if (image < 0)
//                     continue;
//                 if (!dict.TryGetValue(middleIndex, out var imageSet))
//                 {
//                     imageSet = new HashSet<int>();
//                     dict.Add(middleIndex, imageSet);
//                 }
//
//                 imageSet.Add(image);
//             }
//         }
//
//         private static void GetLayer2Texture(MirMapInfo mapInfo, ref Dictionary<int, HashSet<int>> dict)
//         {
//             for (var y = 0; y < mapInfo.Height; y++)
//             for (var x = 0; x < mapInfo.Width; x++)
//             {
//                 var tileInfo = mapInfo.TileInfos[x, y];
//                 var frontIndex = tileInfo.FrontIndex;
//                 var image = (tileInfo.FrontImage & 0x7FFF) - 1;
//                 if (image < 0)
//                     continue;
//
//                 var animation = tileInfo.FrontAnimationFrame & 0x7FFF;
//                 if ((animation & 0x80) > 0)
//                     animation &= 0x7F;
//
//                 if (!dict.TryGetValue(frontIndex, out var imageSet))
//                 {
//                     imageSet = new HashSet<int>();
//                     dict.Add(frontIndex, imageSet);
//                 }
//
//                 if (animation > 0)
//                 {
//                     for (var i = 0; i < animation; i++)
//                         imageSet.Add(image + i);
//                     continue;
//                 }
//
//                 imageSet.Add(image);
//             }
//         }
//     }
// }