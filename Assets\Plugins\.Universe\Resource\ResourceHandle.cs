﻿using System.Threading.Tasks;
using UnityEngine.ResourceManagement.AsyncOperations;

namespace Universe
{
    public struct ResourceHandle<T> : IResourceHandle<T>
    {
        private AsyncOperationHandle<T> _handle;

        public Task<T> Task => _handle.Task;

        public bool IsDisposed { get; private set; }

        public ResourceHandle(AsyncOperationHandle<T> handle)
        {
            IsDisposed = false;
            _handle = handle;
        }

        public void Dispose()
        {
            if (IsDisposed)
                return;

            Dispose(true);
        }

        private void Dispose(bool disposing)
        {
            if (!disposing)
                return;

            _handle.Release();

            IsDisposed = true;
        }

        public bool IsValid()
        {
            return _handle.IsValid();
        }
    }
}