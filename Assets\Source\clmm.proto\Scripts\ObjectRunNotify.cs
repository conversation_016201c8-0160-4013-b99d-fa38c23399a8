﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class ObjectRunNotify : IMessage
    {
        public const int Opcode = 29;

        public int Id => Opcode;
    }

    [Packable(Id = ObjectRunNotify.Opcode)]
    public class ObjectRunNotifyPackable : MessagePackable<ObjectRunNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out ObjectRunNotify message)
        {
            message = new ObjectRunNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}