﻿/*using System.Collections.Generic;
using System.IO;
using Sirenix.OdinInspector;
using UnityEditor;
using UnityEditor.U2D.Sprites;
using UnityEngine;

namespace CLMM.MirTools
{
    public class ActorExport : MonoBehaviour
    {
        private const string DataPath = "E:/Workspase/Project/Crystal/Build/Client/Debug/Data";

        [BoxGroup("Export")]
        [<PERSON><PERSON>("Export Actor Texture", ButtonSizes.Large)]
        [GUIColor(0, 0.9f, 0)]
        private static void ExportActorTexture()
        {
            EditorUtility.DisplayProgressBar("Exporting Actor Texture", "Processing ...", 0);

            try
            {
                var folder = "Monster";

                var files = Directory.GetFiles(Path.Combine(DataPath, folder), "*.lib");
                var index = 0;
                var count = files.Length;
                foreach (var file in files)
                {
                    var filename = Path.GetFileNameWithoutExtension(file);
                    var output = $"Assets/Content/Actor/Texture/{folder}/{filename.Substring(0, 2)}";

                    EditorUtility.DisplayProgressBar(
                        $"Exporting Actor Texture {index}/{count}",
                        output,
                        (float)++index / count);

                    ExportActorTexture(file, output);

                    if (index > 250)
                        return;
                }
            }
            finally
            {
                EditorUtility.ClearProgressBar();
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
                Resources.UnloadUnusedAssets();
            }
        }

        /*[BoxGroup("Export")]
        [Button("Split Actor Texture", ButtonSizes.Large)]
        [GUIColor(0, 0.9f, 0)]
        private static void SplitActorTexture()
        {
            var guids = AssetDatabase.FindAssets("t:Texture", new[] { "Assets/Content/Actor/Texture/Monster" });
            foreach (var guid in guids)
            {
                var path = AssetDatabase.GUIDToAssetPath(guid);
                var directoryName = Path.GetDirectoryName(path);
                var folder = Path.GetFileNameWithoutExtension(directoryName);
                var filename = Path.GetFileNameWithoutExtension(path);
                if (filename.Contains(folder))
                    continue;

                var newPath = path.Replace(folder, $"{folder}/{filename.Substring(0, 2)}");
                var directory = Path.GetDirectoryName(newPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                    AssetDatabase.SaveAssets();
                    AssetDatabase.Refresh();
                }

                var error = AssetDatabase.MoveAsset(path, newPath);
                if (!string.IsNullOrEmpty(error))
                {
                    Debug.LogError(error);
                    return;
                }
            }

            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            Resources.UnloadUnusedAssets();
        }#1#

        private static void ExportActorTexture(string input, string output)
        {
            var filename = Path.GetFileNameWithoutExtension(input);
            output = Path.Combine(output, filename);

            var path = $"{output}.png";
            if (File.Exists(path))
                return;

            var textures = new List<Texture2D>();
            var ids = new List<int>();
            var rects = new List<Rect>();
            var pivots = new List<Vector2>();

            using var lib = new MirLibV2(input);
            for (var i = 0; i < lib.Images.Length; i++)
            {
                var image = lib.LoadImage(i);
                if (image == null || image.Width < 5 || image.Height < 5 || image.Data == null)
                    continue;

                var texture = GenerateTexture2D(image.Width, image.Height, image.Data);
                textures.Add(texture);
                ids.Add(i);
                pivots.Add(new Vector2(
                    (float)-image.X / image.Width,
                    (-16f * 2 + image.Y + image.Height) / image.Height));
            }

            var mainTexture = new Texture2D(4096, 4096);
            var uvRects = mainTexture.PackTextures(textures.ToArray(), 4, 4096);
            foreach (var uv in uvRects)
                rects.Add(new Rect(
                    uv.x * mainTexture.width,
                    uv.y * mainTexture.height,
                    uv.width * mainTexture.width,
                    uv.height * mainTexture.height));

            var directory = Path.GetDirectoryName(path);
            if (!Directory.Exists(directory))
                Directory.CreateDirectory(directory);

            File.WriteAllBytes(path, mainTexture.EncodeToPNG());

            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            Resources.UnloadUnusedAssets();

            GenerateSprite(path, ids, rects, pivots);
        }


        private static Texture2D GenerateTexture2D(int width, int height, IReadOnlyList<byte> source)
        {
            var texture = new Texture2D(width, height);

            var colors = new Color32[width * height];
            for (var y = 0; y < height; y++)
            {
                var flipY = height - 1 - y;
                for (var x = 0; x < width; x++)
                {
                    var index = (flipY * width + x) * 4;
                    colors[y * width + x] = new Color32(
                        source[index + 2], // R
                        source[index + 1], // G
                        source[index], // B
                        source[index + 3] // A
                    );
                }
            }

            texture.SetPixels32(colors);
            texture.Apply();

            return texture;
        }

        private static void GenerateSprite(string path,
            IReadOnlyList<int> ids,
            IReadOnlyList<Rect> rects,
            IReadOnlyList<Vector2> pivots)
        {
            // 强制导入纹理
            // AssetDatabase.ImportAsset(path, ImportAssetOptions.ForceUpdate);

            var textureImporter = AssetImporter.GetAtPath(path) as TextureImporter;
            if (textureImporter == null)
                return;

            var factory = new SpriteDataProviderFactories();
            factory.Init();
            var provider = factory.GetSpriteEditorDataProviderFromObject(textureImporter);
            provider.InitSpriteEditorDataProvider();

            var spriteRects = new List<SpriteRect>();
            var nameFileIdPairs = new List<SpriteNameFileIdPair>();
            for (var i = 0; i < rects.Count; i++)
            {
                var spriteRect = new SpriteRect
                {
                    name = $"{ids[i]}",
                    spriteID = GUID.Generate(),
                    rect = rects[i],
                    alignment = SpriteAlignment.Custom,
                    pivot = pivots[i]
                };
                spriteRects.Add(spriteRect);
                nameFileIdPairs.Add(new SpriteNameFileIdPair(spriteRect.name, spriteRect.spriteID));
            }

            provider.SetSpriteRects(spriteRects.ToArray());

            var spriteNameFileIdDataProvider = provider.GetDataProvider<ISpriteNameFileIdDataProvider>();
            spriteNameFileIdDataProvider.SetNameFileIdPairs(nameFileIdPairs);

            provider.Apply();
            textureImporter.SaveAndReimport();
        }
    }
}*/

