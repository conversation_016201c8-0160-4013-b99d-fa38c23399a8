﻿using System;
using System.Text;
using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class LoginBannedNotify : IMessage
    {
        public const int Opcode = 8;

        public int Id => Opcode;

        public string Reason;
        public DateTime Expiry;
    }

    [Packable(Id = LoginBannedNotify.Opcode)]
    public class LoginBannedNotifyPackable : MessagePackable<LoginBannedNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out LoginBannedNotify message)
        {
            message = new LoginBannedNotify();
            message.Reason = buf.ReadFixedString(Encoding.UTF8);
            message.Expiry = DateTime.FromBinary(buf.ReadLongLE());
        }
    }
}