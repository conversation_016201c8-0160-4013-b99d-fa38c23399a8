﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class ObjectNpcNotify : IMessage
    {
        public const int Opcode = 90;

        public int Id => Opcode;
    }

    [Packable(Id = ObjectNpcNotify.Opcode)]
    public class ObjectNpcNotifyPackable : MessagePackable<ObjectNpcNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out ObjectNpcNotify message)
        {
            message = new ObjectNpcNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}