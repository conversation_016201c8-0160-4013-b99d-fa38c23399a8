using System;
using System.Collections;
using System.Collections.Generic;
using System.Reflection;
using Autofac.Annotation;

namespace Ock
{
    [Component(AutofacScope = AutofacScope.InstancePerLifetimeScope)]
    public class HandlerCollector
    {
        // private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(HandlerCollector));


        [Autowired]
        public readonly IContext Context = null!;

        private readonly Dictionary<Type, IEnumerable> _handlersCache = new();
        private readonly Dictionary<(Type, int), IEnumerable> _maskedHandlersCache = new();

        public IEnumerable<(T instance, HandlerAttribute attribute)> Collect<T>()
        {
            if (!_handlersCache.TryGetValue(typeof(T), out var value)
                || value is not IList<(T, HandlerAttribute)> result)
            {
                result = new List<(T, HandlerAttribute)>();
                _handlersCache.Add(typeof(T), result);

                var handlers = Context.Resolve<IEnumerable<T>>();
                foreach (var handler in handlers)
                {
                    var attribute = handler.GetType().GetCustomAttribute<HandlerAttribute>();
                    if (attribute == null)
                        continue;

                    result.Add((handler, attribute));
                }
            }

            return result;
        }

        public IEnumerable<(T instance, HandlerAttribute attribute)> Collect<T>(int mask)
        {
            if (!_maskedHandlersCache.TryGetValue((typeof(T), mask), out var value)
                || value is not IList<(T, HandlerAttribute)> result)
            {
                result = new List<(T, HandlerAttribute)>();
                _maskedHandlersCache.Add((typeof(T), mask), result);

                var handlers = Collect<T>();
                foreach (var handler in handlers)
                {
                    if (mask == -1 || (mask & handler.attribute.Mask) != 0)
                        result.Add(handler);
                }
            }

            return result;
        }

        public void Clear()
        {
            _maskedHandlersCache.Clear();
            _handlersCache.Clear();
        }
    }
}