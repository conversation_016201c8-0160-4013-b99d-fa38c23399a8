﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class ChangePetModeNotify : IMessage
    {
        public const int Opcode = 61;

        public int Id => Opcode;
    }

    [Packable(Id = ChangePetModeNotify.Opcode)]
    public class ChangePModeNotifyPackable : MessagePackable<ChangePetModeNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out ChangePetModeNotify message)
        {
            message = new ChangePetModeNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}