﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    [Protocol(LogLevel.TRACE)]
    public struct KeepAliveResponse : IMessage
    {
        public const int Opcode = 3;

        public int Id => Opcode;

        public long Time;
    }

    [Packable(Id = KeepAliveResponse.Opcode)]
    public class KeepAliveResponsePackable : MessagePackable<KeepAliveResponse>
    {
        protected override void Deserialize(IByteBuffer buf, out KeepAliveResponse message)
        {
            message = new KeepAliveResponse();
            message.Time = buf.ReadLongLE();
        }
    }
}