﻿using System;
using System.Collections.Generic;
using System.Dynamic;
using Autofac;
using CLMM.Constant;
using CLMM.Proto;
using CLMM.Repository;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Universe;
using ILogger = slf4net.ILogger;

namespace CLMM.UI
{
    [Component(typeof(IMediator), "LobbyUI")]
    public class LobbyUIMediator : Mediator
    {
        private static readonly ILogger Logger = LoggerManager.GetCurrentClassLogger();

        private readonly ILifetimeScope _container;
        private readonly UserRepository _userRepository;

        private SpriteContainer _classAtlas;
        private Image _classImage;
        private TextMeshProUGUI _lastOnlineLabel;
        private TextMeshProUGUI _dataTimeLabel;
        private Button _startButton;

        public LobbyUIMediator(ILifetimeScope container, UserRepository userRepository)
        {
            _container = container;
            _userRepository = userRepository;
        }

        public override void OnCreate()
        {
            _classAtlas = this.UI.GetComponent<SpriteContainer>();

            var vars = this.UI.GetVariables();
            var transforms = vars.Get<List<RectTransform>>("Characters");
            var index = 0;
            foreach (var transform in transforms)
            {
                dynamic userdata = new ExpandoObject();
                userdata.Index = index++;

                var widget = _container.Resolve<IUIWidget>(
                    new NamedParameter("name", "SelCharInfo"),
                    new NamedParameter("transform", transform),
                    new NamedParameter("userdata", userdata));
                widget.SetParent(this.UI, transform.parent);
            }

            _classImage = vars.Get<Image>("ClassImage");
            _lastOnlineLabel = vars.Get<TextMeshProUGUI>("LastOnlineLabel");
            _dataTimeLabel = vars.Get<TextMeshProUGUI>("DataTimeLabel");
            _startButton = vars.Get<Button>("StartButton");

            _startButton.onClick.AddListener(OnStartGameClicked);

            _userRepository.CurrentSelCharInfo.Observe(this.UI, OnCurrentSelCharInfoChanged);
            _userRepository.StartGameState.Observe(this.UI, OnStartGameStateChanged);
        }

        private void OnCurrentSelCharInfoChanged(SelCharInfo info)
        {
            _classImage.sprite = _classAtlas.GetSprite($"{(ClassType)info.Class}[{(GenderType)info.Gender}]");
            if (info.LastAccess == DateTime.MinValue)
            {
                _lastOnlineLabel.enabled = false;
                _dataTimeLabel.alignment = TextAlignmentOptions.Center;
                _dataTimeLabel.text = "Never";
            }
            else
            {
                _lastOnlineLabel.enabled = true;
                _dataTimeLabel.alignment = TextAlignmentOptions.Right;
                _dataTimeLabel.text = $"{info.LastAccess}";
            }
        }

        private void OnStartGameClicked()
        {
            _userRepository.StartGame();
        }

        private void OnStartGameStateChanged((int code, string message) state)
        {
            _startButton.interactable = state.code <= 0;
        }
    }
}