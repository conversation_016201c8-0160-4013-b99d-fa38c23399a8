%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 468a46d0ae32c3544b7d98094e6448a9, type: 3}
  m_Name: AddressableAssetSettings
  m_EditorClassIdentifier: 
  m_DefaultGroup: 9230f8ffceb521745af1ad55001c4ede
  m_currentHash:
    serializedVersion: 2
    Hash: 39416972ef0b12b08b8935f0cf7b8832
  m_OptimizeCatalogSize: 0
  m_BuildRemoteCatalog: 1
  m_CatalogRequestsTimeout: 0
  m_DisableCatalogUpdateOnStart: 0
  m_InternalIdNamingMode: 0
  m_InternalBundleIdMode: 1
  m_AssetLoadMode: 0
  m_BundledAssetProviderType:
    m_AssemblyName: Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    m_ClassName: UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider
  m_AssetBundleProviderType:
    m_AssemblyName: Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    m_ClassName: UnityEngine.ResourceManagement.ResourceProviders.AssetBundleProvider
  m_IgnoreUnsupportedFilesInBuild: 0
  m_UniqueBundleIds: 0
  m_EnableJsonCatalog: 0
  m_NonRecursiveBuilding: 1
  m_CCDEnabled: 0
  m_maxConcurrentWebRequests: 3
  m_UseUWRForLocalBundles: 0
  m_BundleTimeout: 0
  m_BundleRetryCount: 0
  m_BundleRedirectLimit: -1
  m_SharedBundleSettings: 0
  m_SharedBundleSettingsCustomGroupIndex: 0
  m_ContiguousBundles: 1
  m_StripUnityVersionFromBundleBuild: 0
  m_DisableVisibleSubAssetRepresentations: 0
  m_BuiltInBundleNaming: 0
  mBuiltInBundleCustomNaming: 
  m_MonoScriptBundleNaming: 0
  m_CheckForContentUpdateRestrictionsOption: 0
  m_MonoScriptBundleCustomNaming: 
  m_RemoteCatalogBuildPath:
    m_Id: 5f342fe803d39054cbb133c458667a33
  m_RemoteCatalogLoadPath:
    m_Id: adc74e48c2d56ad49a810ebd6703431f
  m_ContentStateBuildPathProfileVariableName: <default settings path>
  m_CustomContentStateBuildPath: 
  m_ContentStateBuildPath: 
  m_BuildAddressablesWithPlayerBuild: 0
  m_overridePlayerVersion: '[UnityEditor.PlayerSettings.bundleVersion]'
  m_GroupAssets:
  - {fileID: 11400000, guid: 600c3fa5a88141b43813cc53fad6552c, type: 2}
  - {fileID: 11400000, guid: 1b846deeb32aaf64c9921cce1bb81812, type: 2}
  - {fileID: 11400000, guid: 53f7d843ac6ea7847b0a5d63c940419e, type: 2}
  - {fileID: 11400000, guid: dff5609f628243741b034b3b5b5bcb17, type: 2}
  - {fileID: 11400000, guid: a1f1244952a40544ba2db0231edf39af, type: 2}
  m_BuildSettings:
    m_LogResourceManagerExceptions: 1
    m_BundleBuildPath: Temp/com.unity.addressables/AssetBundles
  m_ProfileSettings:
    m_Profiles:
    - m_InheritedParent: 
      m_Id: dc21787566cd5bc4ca9100152cbfee5d
      m_ProfileName: Default
      m_Values:
      - m_Id: 2191ba90186cb284ca1d0e433799137d
        m_Value: '{UnityEngine.AddressableAssets.Addressables.RuntimePath}/[BuildTarget]'
      - m_Id: 5f342fe803d39054cbb133c458667a33
        m_Value: 'ServerData/[BuildTarget]'
      - m_Id: 8feab3c0570838940867d06cdc93bd1c
        m_Value: '[UnityEditor.EditorUserBuildSettings.activeBuildTarget]'
      - m_Id: adc74e48c2d56ad49a810ebd6703431f
        m_Value: 'http://[PrivateIpAddress]:[HostingServicePort]'
      - m_Id: b61fc828b7d87744699312ee43c71433
        m_Value: '[UnityEngine.AddressableAssets.Addressables.BuildPath]/[BuildTarget]'
    m_ProfileEntryNames:
    - m_Id: 2191ba90186cb284ca1d0e433799137d
      m_Name: Local.LoadPath
      m_InlineUsage: 0
    - m_Id: 5f342fe803d39054cbb133c458667a33
      m_Name: Remote.BuildPath
      m_InlineUsage: 0
    - m_Id: 8feab3c0570838940867d06cdc93bd1c
      m_Name: BuildTarget
      m_InlineUsage: 0
    - m_Id: adc74e48c2d56ad49a810ebd6703431f
      m_Name: Remote.LoadPath
      m_InlineUsage: 0
    - m_Id: b61fc828b7d87744699312ee43c71433
      m_Name: Local.BuildPath
      m_InlineUsage: 0
    m_ProfileVersion: 1
  m_LabelTable:
    m_LabelNames:
    - default
    - UI
  m_SchemaTemplates: []
  m_GroupTemplateObjects:
  - {fileID: 11400000, guid: 50b2622a305902d498567afffef2c368, type: 2}
  m_InitializationObjects: []
  m_CertificateHandlerType:
    m_AssemblyName: 
    m_ClassName: 
  m_ActivePlayerDataBuilderIndex: 2
  m_DataBuilders:
  - {fileID: 11400000, guid: e3dcdf8866837a642acd9426c18def2b, type: 2}
  - {fileID: 11400000, guid: 0b2a73ebd52090e4c93871c6f8459584, type: 2}
  - {fileID: 11400000, guid: 529799d4acd4c3643b4782427d95fbfa, type: 2}
  m_ActiveProfileId: dc21787566cd5bc4ca9100152cbfee5d
