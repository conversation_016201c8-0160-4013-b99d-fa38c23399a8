﻿using System;
using System.Collections.Generic;
using Autofac.Annotation;
using DotNetty.Buffers;
using slf4net;

namespace Ock
{
    [Component(typeof(IProtocolSerializer), AutofacScope = AutofacScope.InstancePerLifetimeScope)]
    public class ProtocolSerializer : IProtocolSerializer
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(ProtocolSerializer));


        private readonly Dictionary<int, IMessagePackable> _serializeMappings = new();
        private readonly Dictionary<int, IMessagePackable> _deserializeMappings = new();

        public void Register(IMessagePackable packable)
        {
            if (Attribute.GetCustomAttribute(packable.GetType(),
                    typeof(PackableAttribute)) is not PackableAttribute attribute)
                return;

            try
            {
                if (attribute.Requested)
                    _serializeMappings.Add(attribute.Id, packable);
                else
                    _deserializeMappings.Add(attribute.Id, packable);
            }
            catch (Exception e)
            {
                if (Logger.IsErrorEnabled)
                    Logger.Error($"{nameof(Register)}: -I {packable.GetType()} {attribute.Id}\r\nException - {e}");
            }
        }

        public void Unregister(IMessagePackable packable)
        {
            if (Attribute.GetCustomAttribute(packable.GetType(),
                    typeof(PackableAttribute)) is not PackableAttribute attribute)
                return;

            if (attribute.Requested)
                _serializeMappings.Remove(attribute.Id);
            else
                _deserializeMappings.Remove(attribute.Id);
        }

        public void Serialize(IByteBuffer buf, IMessage message)
        {
            var id = message.Id;
            buf.WriteUnsignedShortLE((ushort)id);

            if (!_serializeMappings.TryGetValue(id, out var packable))
                throw new KeyNotFoundException($"The Id:{id} is not found in the serializers");

            if (Logger.IsTraceEnabled)
                Logger.Trace($"{nameof(Serialize)} message with serializer: {packable.GetType()}");

            packable.Serialize(buf, message);
        }

        public object Deserialize(IByteBuffer buf)
        {
            var id = buf.ReadUnsignedShortLE();
            if (!_deserializeMappings.TryGetValue(id, out var packable))
                throw new KeyNotFoundException($"The opcode: {id} is not found in the deserializers");

            if (Logger.IsTraceEnabled)
                Logger.Trace($"{nameof(Deserialize)} message with packable: {packable.GetType()}");

            return packable.Deserialize(buf);
        }
    }
}