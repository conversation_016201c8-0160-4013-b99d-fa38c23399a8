using System;
using System.Threading.Tasks;
using DotNetty.Handlers.Timeout;
using DotNetty.Transport.Channels;
using Newtonsoft.Json;
using slf4net;

namespace Ock
{
    public class SessionHandler : ChannelHandlerAdapter
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(SessionHandler));


        private readonly IMessageQueue _messageQueue;
        private readonly IMessageFilter _messageFilter;

        public SessionHandler(IMessageQueue messageQueue, IMessageFilter messageFilter)
        {
            _messageQueue = messageQueue;
            _messageFilter = messageFilter;
        }

        public override void ChannelInactive(IChannelHandlerContext context)
        {
            if (Logger.IsInfoEnabled)
                Logger.Info($"{nameof(ChannelInactive)} {context.Channel.RemoteAddress}");

            base.ChannelInactive(context);
        }

        public override void ChannelRead(IChannelHandlerContext context, object message)
        {
            try
            {
                if (Logger.IsInfoEnabled)
                {
                    var type = message.GetType();
                    if (!_messageFilter.ShouldFilter(type, LogLevel.INFO))
                        Logger.Info($"{nameof(ChannelRead)}: -I {type.Name} {JsonConvert.SerializeObject(message)}");
                }

                _messageQueue.Enqueue(((ISession)context.Channel, (IMessage)message));
            }
            catch (Exception e)
            {
                if (Logger.IsErrorEnabled)
                    Logger.Error($"{nameof(ChannelRead)}: Error dispatch packet.\r\nException - {e}");
            }
        }

        public override Task WriteAsync(IChannelHandlerContext context, object message)
        {
            if (Logger.IsInfoEnabled)
            {
                var type = message.GetType();
                if (!_messageFilter.ShouldFilter(type, LogLevel.INFO))
                    Logger.Info($"{nameof(WriteAsync)}: -I {type.Name} {JsonConvert.SerializeObject(message)}");
            }

            return context.WriteAsync(message);
        }

        public override void ExceptionCaught(IChannelHandlerContext context, Exception exception)
        {
            if (Logger.IsInfoEnabled)
                Logger.Info($"{nameof(ExceptionCaught)}: Closing channel.\r\nException - {exception}");
        }

        public override void UserEventTriggered(IChannelHandlerContext context, object evt)
        {
            if (evt is IdleStateEvent)
            {
                var e = (IdleStateEvent)evt;
                if (e.State == IdleState.WriterIdle)
                    _messageQueue.Enqueue(((ISession)context.Channel, new WriterIdleNotify()));
            }

            base.UserEventTriggered(context, evt);
        }
    }
}