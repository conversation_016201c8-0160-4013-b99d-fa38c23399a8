%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!181963792 &2655988077585873504
Preset:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: EquipmentTextureImporter
  m_TargetType:
    m_NativeTypeID: 1006
    m_ManagedTypePPtr: {fileID: 0}
    m_ManagedTypeFallback: 
  m_Properties:
  - target: {fileID: 0}
    propertyPath: m_ExternalObjects.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_MipMapMode
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableMipMap
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_sRGBTexture
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LinearTexture
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_FadeOut
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_BorderMipMap
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_MipMapsPreserveCoverage
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_AlphaTestReferenceValue
    value: 0.5
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_MipMapFadeDistanceStart
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_MipMapFadeDistanceEnd
    value: 3
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ConvertToNormalMap
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ExternalNormalMap
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HeightScale
    value: 0.25
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_NormalMapFilter
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_FlipGreenChannel
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsReadable
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_StreamingMipmaps
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_StreamingMipmapsPriority
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_VTOnly
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IgnoreMipmapLimit
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrayScaleToAlpha
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GenerateCubemap
    value: 6
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_CubemapConvolution
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SeamlessCubemap
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TextureFormat
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_MaxTextureSize
    value: 2048
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TextureSettings.m_FilterMode
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TextureSettings.m_Aniso
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TextureSettings.m_MipBias
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TextureSettings.m_WrapU
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TextureSettings.m_WrapV
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TextureSettings.m_WrapW
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_NPOTScale
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_Lightmap
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_CompressionQuality
    value: 50
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SpriteMode
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SpriteExtrude
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SpriteMeshType
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_Alignment
    value: 9
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SpritePivot.x
    value: 0.5
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SpritePivot.y
    value: 0.5
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SpritePixelsToUnits
    value: 16
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SpriteBorder.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SpriteBorder.y
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SpriteBorder.z
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SpriteBorder.w
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SpriteGenerateFallbackPhysicsShape
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_AlphaUsage
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_AlphaIsTransparency
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SpriteTessellationDetail
    value: -1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TextureType
    value: 8
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TextureShape
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SingleChannelComponent
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_FlipbookRows
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_FlipbookColumns
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_MaxTextureSizeSet
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_CompressionQualitySet
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TextureFormatSet
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IgnorePngGamma
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ApplyGammaDecoding
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_Swizzle
    value: 50462976
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_CookieLightType
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.size
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[0].m_BuildTarget
    value: DefaultTexturePlatform
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[0].m_MaxTextureSize
    value: 4096
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[0].m_ResizeAlgorithm
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[0].m_TextureFormat
    value: -1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[0].m_TextureCompression
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[0].m_CompressionQuality
    value: 100
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[0].m_CrunchedCompression
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[0].m_AllowsAlphaSplitting
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[0].m_Overridden
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[0].m_IgnorePlatformSupport
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[0].m_AndroidETC2FallbackOverride
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[0].m_ForceMaximumCompressionQuality_BC6H_BC7
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[1].m_BuildTarget
    value: Standalone
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[1].m_MaxTextureSize
    value: 2048
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[1].m_ResizeAlgorithm
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[1].m_TextureFormat
    value: -1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[1].m_TextureCompression
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[1].m_CompressionQuality
    value: 50
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[1].m_CrunchedCompression
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[1].m_AllowsAlphaSplitting
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[1].m_Overridden
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[1].m_IgnorePlatformSupport
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[1].m_AndroidETC2FallbackOverride
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PlatformSettings.Array.data[1].m_ForceMaximumCompressionQuality_BC6H_BC7
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SpriteSheet.m_Sprites.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SpriteSheet.m_Outline.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SpriteSheet.m_PhysicsShape.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SpriteSheet.m_Bones.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SpriteSheet.m_SpriteID
    value: 5e97eb03825dee720800000000000000
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SpriteSheet.m_InternalID
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SpriteSheet.m_Vertices.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SpriteSheet.m_Indices.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SpriteSheet.m_Edges.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SpriteSheet.m_Weights.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SpriteSheet.m_SecondaryTextures.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SpriteSheet.m_NameFileIdTable.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_MipmapLimitGroupName
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PSDRemoveMatte
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UserData
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_AssetBundleName
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_AssetBundleVariant
    value: 
    objectReference: {fileID: 0}
  m_ExcludedProperties: []
