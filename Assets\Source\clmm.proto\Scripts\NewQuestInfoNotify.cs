﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    [Protocol(LogLevel.DEBUG)]
    public class NewQuestInfoNotify : IMessage
    {
        public const int Opcode = 202;

        public int Id => Opcode;
    }

    [Packable(Id = NewQuestInfoNotify.Opcode)]
    public class NewQuestInfoNotifyPackable : MessagePackable<NewQuestInfoNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out NewQuestInfoNotify message)
        {
            message = new NewQuestInfoNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}