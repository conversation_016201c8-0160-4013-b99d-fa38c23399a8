﻿/*using System;

namespace Ock
{
    public enum Scope
    {
        InstancePerDependency,
        SingleInstance,
        InstancePerLifetimeScope
    }

    [AttributeUsage(AttributeTargets.Class, Inherited = false)]
    public class InjectableAttribute : Attribute
    {
        public Type Type { get; }
        public string Name { get; }
        public Scope Scope { get; set; }

        public InjectableAttribute() { }

        public InjectableAttribute(Type type)
        {
            Type = type;
        }

        public InjectableAttribute(Type type, string name) : this(type)
        {
            Name = name;
        }
    }
}*/