﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class AddBuffNotify : IMessage
    {
        public const int Opcode = 142;

        public int Id => Opcode;
    }

    [Packable(Id = AddBuffNotify.Opcode)]
    public class AddBuffNotifyPackable : MessagePackable<AddBuffNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out AddBuffNotify message)
        {
            message = new AddBuffNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}