﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class ObjectTurnNotify : IMessage
    {
        public const int Opcode = 27;

        public int Id => Opcode;
    }

    [Packable(Id = ObjectTurnNotify.Opcode)]
    public class ObjectTurnNotifyPackable : MessagePackable<ObjectTurnNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out ObjectTurnNotify message)
        {
            message = new ObjectTurnNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}