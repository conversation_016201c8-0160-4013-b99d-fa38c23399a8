﻿using System;
using System.Collections.Generic;
using DotNetty.Buffers;
using slf4net;

namespace Universe
{
    public class ProtocolSerializer : IProtocolSerializer
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(ProtocolSerializer));

        private readonly Dictionary<Type, IMessagePackable> _typeMappings = new();
        private readonly Dictionary<int, IMessagePackable> _idMappings = new();

        public void Register(IMessagePackable packable)
        {
            if (Attribute.GetCustomAttribute(packable.GetType(),
                    typeof(PackableAttribute)) is not PackableAttribute attribute)
                return;
            if (attribute.Type != null)
                _typeMappings.Add(attribute.Type, packable);
            if (attribute.Id >= 0)
                _idMappings.Add(attribute.Id, packable);
        }

        public void Unregister(IMessagePackable packable)
        {
            if (Attribute.GetCustomAttribute(packable.GetType(),
                    typeof(PackableAttribute)) is not PackableAttribute attribute)
                return;
            _typeMappings.Remove(attribute.Type);
            _idMappings.Remove(attribute.Id);
        }

        public void Serialize(IByteBuffer buf, IMessage message)
        {
            buf.WriteUnsignedShortLE((ushort)message.Id);
            var type = message.GetType();
            if (!_typeMappings.TryGetValue(type, out var packable))
                throw new KeyNotFoundException($"The type:{type} is not found in the serializers");

            if (Logger.IsTraceEnabled)
                Logger.Trace($"Serialize packet with serializer: {packable.GetType()}");

            packable.Serialize(buf, message);
        }

        public object Deserialize(IByteBuffer buf)
        {
            var id = buf.ReadUnsignedShortLE();
            if (!_idMappings.TryGetValue(id, out var packable))
                throw new KeyNotFoundException($"The opcode: {id} is not found in the deserializers");

            if (Logger.IsTraceEnabled)
                Logger.Trace($"Deserialize packet with serializer: {packable.GetType()}");

            return packable.Deserialize(buf);
        }
    }
}