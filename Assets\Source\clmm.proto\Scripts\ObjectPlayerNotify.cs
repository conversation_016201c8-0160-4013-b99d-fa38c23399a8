﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class ObjectPlayerNotify : IMessage
    {
        public const int Opcode = 24;

        public int Id => Opcode;
    }

    [Packable(Id = ObjectPlayerNotify.Opcode)]
    public class ObjectPlayerNotifyPackable : MessagePackable<ObjectPlayerNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out ObjectPlayerNotify message)
        {
            message = new ObjectPlayerNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}