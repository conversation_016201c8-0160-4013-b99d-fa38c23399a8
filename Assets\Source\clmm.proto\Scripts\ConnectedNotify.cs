﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class ConnectedNotify : IMessage
    {
        public const int Opcode = 0;

        public int Id => Opcode;
    }

    [Packable(Id = ConnectedNotify.Opcode)]
    public class ConnectedNotifyPackable : MessagePackable<ConnectedNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out ConnectedNotify message)
        {
            message = new ConnectedNotify();
        }
    }
}