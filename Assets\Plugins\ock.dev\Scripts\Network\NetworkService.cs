﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Autofac.Annotation;
using DotNetty.Common.Internal.Logging;
using DotNetty.Transport.Channels;
using NLog.Extensions.Logging;
using slf4net;

namespace Ock
{
    [Component(typeof(IService), "Network", Services = new[] { typeof(IService) }, AutofacScope = AutofacScope.InstancePerLifetimeScope)]
    public class NetworkService : IService
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(NetworkService));

        static NetworkService()
        {
            InternalLoggerFactory.DefaultFactory = new NLogLoggerFactory();
        }

        public IEventLoopGroup EventLoopGroup { get; private set; }

        private readonly IMessageDispatcher _messageDispatcher;
        private readonly IMessageQueue _messageQueue;
        private readonly List<(ISession session, IMessage message)> _messageCache = new();

        public NetworkService(IContext context)
        {
            _messageDispatcher = context.Resolve<IMessageDispatcher>();
            _messageQueue = context.Resolve<IMessageQueue>();
        }

        public void Initialize()
        {
            EventLoopGroup = new SingleThreadEventLoop();
        }

        public void Execute()
        {
            _messageQueue.SwapTo(_messageCache);
            if (_messageCache.Count == 0)
                return;

            try
            {
                foreach (var (session, message) in _messageCache)
                    _messageDispatcher.Dispatch(session, message);
            }
            finally
            {
                _messageCache.Clear();
            }
        }

        public void Shutdown()
        {
#if UNITY_EDITOR
            EventLoopGroup?.ShutdownGracefullyAsync(
                    TimeSpan.FromMilliseconds(100), TimeSpan.FromSeconds(1))
                .ContinueWith(task =>
                {
                    if (Logger.IsErrorEnabled)
                        Logger.Error($"{nameof(Shutdown)}.\r\nException - {task.Exception}");
                }, TaskContinuationOptions.OnlyOnFaulted);
            EventLoopGroup = null;
#endif // UNITY_EDITOR
        }
    }
}