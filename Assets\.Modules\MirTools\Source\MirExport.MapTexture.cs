﻿using System;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEditor.U2D.Sprites;
using UnityEngine;

namespace CLMM.MirTools
{
    public static partial class MirExport
    {
        [MenuItem("MitTools/Batch Set Sprite Pivot")]
        private static void BatchSetSpritePivot()
        {
            var guids = AssetDatabase.FindAssets("t:Texture", new[] { $"Assets/Content/Map/Texture/WemadeMir2" });
            foreach (var guid in guids)
            {
                var path = AssetDatabase.GUIDToAssetPath(guid);
                var textureImporter = AssetImporter.GetAtPath(path) as TextureImporter;
                if (textureImporter == null)
                    return;

                var factory = new SpriteDataProviderFactories();
                factory.Init();
                var provider = factory.GetSpriteEditorDataProviderFromObject(textureImporter);
                provider.InitSpriteEditorDataProvider();

                var spriteRects = provider.GetSpriteRects();
                foreach (var spriteRect in spriteRects)
                {
                    var size = spriteRect.rect.size;
                    var width = (int)size.x;
                    var height = (int)size.y;
                    spriteRect.alignment = width == 48 && height == 32 || width == 96 && height == 64
                        ? SpriteAlignment.Center
                        : SpriteAlignment.BottomLeft;
                }
                provider.SetSpriteRects(spriteRects);

                provider.Apply();
                textureImporter.SaveAndReimport();
            }
        }
        [MenuItem("MitTools/Export All Map Textures")]
        private static void ExportAllMapTextures()
        {
            EditorUtility.DisplayProgressBar("Exporting All Map Textures", "Processing ...", 0);

            try
            {
                const string folder = "E:/Workspace/Project/Crystal/Build/Client/Debug/Data";

                var fileMapping = new Dictionary<string, (string, int)>
                {
                    { "Map/WemadeMir2", ("Map/Texture/WemadeMir2", 2048) }
                };

                foreach (var (key, (path, size)) in fileMapping)
                {
                    var files = Directory.GetFiles(Path.Combine(folder, key), "*.lib");
                    foreach (var file in files)
                    {
                        var filename = Path.GetFileNameWithoutExtension(file);
                        var output = $"Assets/Content/{path}/{filename}";

                        GenerateMapTextures(file, output, size);
                    }
                }
            }
            finally
            {
                EditorUtility.ClearProgressBar();
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
                Resources.UnloadUnusedAssets();
            }
        }

        private static void GenerateMapTextures(string input, string output, int size)
        {
            if (Directory.Exists(output))
                return;

            var textures = new List<Texture2D>();
            var ids = new List<int>();

            using var lib = new MirLibV2(input);
            var count = lib.Images.Length;
            for (var i = 0; i < count; i++)
            {
                var image = lib.LoadImage(i);
                if (image == null)
                    continue;
                if (image.Width <= 4 || image.Height <= 4 || image.Data == null)
                    continue;

                EditorUtility.DisplayProgressBar(
                    $"Exporting Texture {i}/{count}",
                    output,
                    (float)i / count);

                var texture = GenerateTexture2D(image.Width, image.Height, image.Data);
                textures.Add(texture);
                ids.Add(i);
            }

            var index = -1;
            while (textures.Count > 0)
            {
                var texture = GenerateMergeTexture(textures, ids, size, out var uvRects);

                var rects = new List<Rect>();
                foreach (var uv in uvRects)
                {
                    rects.Add(new Rect(
                        uv.x * texture.width,
                        uv.y * texture.height,
                        uv.width * texture.width,
                        uv.height * texture.height));
                }

                textures.RemoveRange(0, uvRects.Length);

                var path = $"{output}/{index++}.png";
                var directory = Path.GetDirectoryName(path);
                if (!Directory.Exists(directory))
                    Directory.CreateDirectory(directory);

                File.WriteAllBytes(path, texture.EncodeToPNG());

                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
                Resources.UnloadUnusedAssets();

                GenerateMapSprite(path, ids, rects);

                ids.RemoveRange(0, uvRects.Length);
            }
        }

        private static Texture2D GenerateMergeTexture(List<Texture2D> textures, IReadOnlyList<int> ids, int size,
            out Rect[] rects)
        {
            var maxArea = (int)((size + 4) * (size + 4) * 1f);
            var totalArea = 0;
            var count = 0;
            foreach (var texture in textures)
            {
                var area = (texture.width + 4) * (texture.height + 4);
                totalArea += area;
                if (totalArea > maxArea)
                    break;
                count++;
            }

            if (count == 0)
                throw new Exception("beyond max area, no texture to merge.");

            rects = null;
            var mainTexture = new Texture2D(size, size);
            var success = false;
            while (!success)
            {
                if (count < textures.Count)
                {
                    for (var i = count - 2; i >= 0; i--)
                    {
                        if (ids[i + 1] - ids[i] != 1)
                        {
                            count = i + 1;
                            break;
                        }
                    }
                }

                textures = textures.GetRange(0, count);
                rects = mainTexture.PackTextures(textures.ToArray(), 4, size);

                if (mainTexture.width >= size
                    && mainTexture.height >= size
                    && CheckTextureSizeChanged(textures, rects, size))
                {
                    count--;
                    continue;
                }

                success = true;
            }

            return mainTexture;
        }

        // 检测PackTextures生成的Rect[]尺寸是否有变化
        private static bool CheckTextureSizeChanged(List<Texture2D> textures, Rect[] rects, int size)
        {
            for (var i = 0; i < rects.Length; i++)
            {
                var rect = rects[i];
                var texture = textures[i];
                if ((int)(rect.width * size) != texture.width
                    || (int)(rect.height * size) != texture.height)
                    return true;
            }

            return false;
        }

        private static Texture2D GenerateTexture2D(int width, int height, IReadOnlyList<byte> source)
        {
            var texture = new Texture2D(width, height);

            var colors = new Color32[width * height];
            for (var y = 0; y < height; y++)
            {
                var flipY = height - 1 - y;
                for (var x = 0; x < width; x++)
                {
                    var index = (flipY * width + x) * 4;
                    colors[y * width + x] = new Color32(
                        source[index + 2], // R
                        source[index + 1], // G
                        source[index], // B
                        source[index + 3] // A
                    );
                }
            }

            texture.SetPixels32(colors);
            texture.Apply();

            return texture;
        }

        private static void GenerateMapSprite(string path,
            IReadOnlyList<int> ids,
            IReadOnlyList<Rect> rects)
        {
            var textureImporter = AssetImporter.GetAtPath(path) as TextureImporter;
            if (textureImporter == null)
                return;

            var factory = new SpriteDataProviderFactories();
            factory.Init();
            var provider = factory.GetSpriteEditorDataProviderFromObject(textureImporter);
            provider.InitSpriteEditorDataProvider();

            var spriteRects = new List<SpriteRect>();
            var nameFileIdPairs = new List<SpriteNameFileIdPair>();
            for (var i = 0; i < rects.Count; i++)
            {
                var spriteRect = new SpriteRect
                {
                    name = $"{ids[i]}",
                    spriteID = GUID.Generate(),
                    rect = rects[i],
                };

                var size = spriteRect.rect.size;
                var width = (int)size.x;
                var height = (int)size.y;
                if (width == 48 && height == 32 || width == 96 && height == 64)
                    spriteRect.alignment = SpriteAlignment.Center;
                else
                    spriteRect.alignment = SpriteAlignment.BottomLeft;

                spriteRects.Add(spriteRect);
                nameFileIdPairs.Add(new SpriteNameFileIdPair(spriteRect.name, spriteRect.spriteID));
            }

            provider.SetSpriteRects(spriteRects.ToArray());

            var spriteNameFileIdDataProvider = provider.GetDataProvider<ISpriteNameFileIdDataProvider>();
            spriteNameFileIdDataProvider.SetNameFileIdPairs(nameFileIdPairs);

            provider.Apply();
            textureImporter.SaveAndReimport();
        }
    }
}