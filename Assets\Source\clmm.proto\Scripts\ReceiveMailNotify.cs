﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class ReceiveMailNotify : IMessage
    {
        public const int Opcode = 229;

        public int Id => Opcode;
    }

    [Packable(Id = ReceiveMailNotify.Opcode)]
    public class ReceiveMailNotifyPackable : MessagePackable<ReceiveMailNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out ReceiveMailNotify message)
        {
            message = new ReceiveMailNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}