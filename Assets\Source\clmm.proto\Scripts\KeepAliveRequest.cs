﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    [Protocol(LogLevel.TRACE)]
    public struct KeepAliveRequest : IMessage
    {
        public const int Opcode = 2;

        public int Id => Opcode;

        public long Time;
    }

    [Packable(Id = KeepAliveRequest.Opcode, Requested = true)]
    public class KeepAliveRequestPackable : MessagePackable<KeepAliveRequest>
    {
        protected override void Serialize(IByteBuffer buf, KeepAliveRequest message)
        {
            buf.WriteLongLE(message.Time);
        }
    }
}