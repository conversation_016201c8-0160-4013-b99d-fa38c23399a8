﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class DamageIndicatorNotify : IMessage
    {
        public const int Opcode = 73;

        public int Id => Opcode;
    }

    [Packable(Id = DamageIndicatorNotify.Opcode)]
    public class DamageIndicatorNotifyPackable : MessagePackable<DamageIndicatorNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out DamageIndicatorNotify message)
        {
            message = new DamageIndicatorNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}