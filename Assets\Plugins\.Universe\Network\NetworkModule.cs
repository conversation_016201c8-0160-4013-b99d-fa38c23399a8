using Autofac;

namespace Universe
{
    public sealed class NetworkModule : Module
    {
        protected override void Load(ContainerBuilder builder)
        {
            builder.RegisterType<NetworkService>().Named<IService>("Network")
                .AsImplementedInterfaces()
                .As<NetworkService>()
                .SingleInstance();
            builder.RegisterType<Connector>().As<IConnector>().InstancePerLifetimeScope();
            builder.RegisterType<ProtocolSerializer>().As<IProtocolSerializer>().InstancePerLifetimeScope();
            builder.RegisterType<MessageDispatcher>().As<IMessageDispatcher>().InstancePerLifetimeScope();
            builder.RegisterType<MessageFilter>().As<IMessageFilter>().InstancePerLifetimeScope();
        }
    }
}