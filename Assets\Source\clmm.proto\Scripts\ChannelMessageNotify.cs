﻿using System.Text;
using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class ChannelMessageNotify : IMessage
    {
        public const int Opcode = 30;

        public int Id => Opcode;

        public string Content;
        public byte ChannelType;
    }

    [Packable(Id = ChannelMessageNotify.Opcode)]
    public class ChatNotifyPackable : MessagePackable<ChannelMessageNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out ChannelMessageNotify message)
        {
            message = new ChannelMessageNotify();
            message.Content = buf.ReadFixedString(Encoding.UTF8);
            message.ChannelType = buf.ReadByte();
        }
    }
}