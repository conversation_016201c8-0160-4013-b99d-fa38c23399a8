﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class GuildBuffListNotify : IMessage
    {
        public const int Opcode = 246;

        public int Id => Opcode;
    }

    [Packable(Id = GuildBuffListNotify.Opcode)]
    public class GuildBuffListNotifyPackable : MessagePackable<GuildBuffListNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out GuildBuffListNotify message)
        {
            message = new GuildBuffListNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}