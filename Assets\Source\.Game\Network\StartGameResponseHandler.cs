using CLMM.Constant;
using CLMM.Proto;
using CLMM.Repository;
using slf4net;
using Universe;
using JsonConvert = Newtonsoft.Json.JsonConvert;

namespace CLMM.Network
{
    [Component]
    [Handler((int)MessageId.StartGameResponse, Tag = (int)GroupId.Lobby)]
    public class StartGameResponseHandler : MessageHandler<StartGameResponse>
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(StartGameResponseHandler));

        private readonly UserRepository _userRepository;

        public StartGameResponseHandler(UserRepository userRepository)
        {
            _userRepository = userRepository;
        }

        protected override void Handle(ISession session, StartGameResponse message)
        {
            if (Logger.IsDebugEnabled)
                Logger.Debug($"{nameof(Handle)}: -I {nameof(StartGameResponse)} {JsonConvert.SerializeObject(message)}");

            _userRepository.OnStartGame(message.Result);
        }
    }
}