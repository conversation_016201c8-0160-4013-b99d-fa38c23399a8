using System;
using System.Collections.Generic;
using Autofac.Annotation;
using Newtonsoft.Json;
using slf4net;

namespace Ock
{
    [Component(typeof(IEventDispatcher), AutofacScope = AutofacScope.InstancePerLifetimeScope)]
    public class EventDispatcher : IEventDispatcher
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(EventDispatcher));


        private readonly Dictionary<int, List<IEventHandler>> _handlerMappings = new();

        private readonly List<IEventHandler> _handlerCache = new();

        public void Register(int id, IEventHandler handler)
        {
            if (Logger.IsTraceEnabled)
                Logger.Trace($"{nameof(Register)}: -I {id} {handler}");

            if (!_handlerMappings.TryGetValue(id, out var handlers))
            {
                handlers = new List<IEventHandler>();
                _handlerMappings.Add(id, handlers);
            }

            handlers.Add(handler);
        }
        public void Unregister(int id, IEventHandler handler)
        {
            if (Logger.IsTraceEnabled)
                Logger.Trace($"{nameof(Unregister)}: -I {id} {handler}");

            if (!_handlerMappings.TryGetValue(id, out var handlers))
                return;

            handlers.Remove(handler);
        }
        public void Dispatch(IEvent @event)
        {
            if (!_handlerMappings.TryGetValue(@event.Id, out var handlers))
                return;

            _handlerCache.AddRange(handlers);

            foreach (var handler in _handlerCache)
            {
                try
                {
                    handler.Handle(@event);
                }
                catch (Exception e)
                {
                    if (Logger.IsErrorEnabled)
                        Logger.Error($"{nameof(Dispatch)}: -I {JsonConvert.SerializeObject(@event)}\r\nException - {e}");
                }
            }

            _handlerCache.Clear();
        }
    }
}