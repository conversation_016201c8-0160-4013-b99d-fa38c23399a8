using System;
using System.Linq;
using System.Reflection;

namespace Ock
{
    public static class NetworkExtensions
    {
        public static void RegisterPackables<TProtocolSerializer, TMessagePackable>(this IContext context, Assembly assembly) where TProtocolSerializer : IProtocolSerializer where TMessagePackable : IMessagePackable
        {
            var serializer = context.Resolve<TProtocolSerializer>();
            var types = assembly.GetTypes()
                .Where(type => typeof(TMessagePackable).IsAssignableFrom(type)
                               && !type.IsAbstract
                               && !type.IsInterface);

            foreach (var type in types)
                serializer.Register((TMessagePackable)Activator.CreateInstance(type));
        }

        public static void RegisterFilter<TMessageFilter, TMessage>(this IContext context, Assembly assembly) where TMessageFilter : IMessageFilter where TMessage : IMessage
        {
            var filter = context.Resolve<TMessageFilter>();
            var types = assembly.GetTypes()
                .Where(type => typeof(TMessage).IsAssignableFrom(type)
                               && !type.IsAbstract
                               && !type.IsInterface);

            foreach (var type in types)
            {
                var attribute = type.GetCustomAttribute<ProtocolAttribute>();
                if (attribute == null)
                    continue;
                filter.AddRule(type, attribute.LogLevel);
            }
        }

        public static void RegisterHandlers<TMessageDispatcher, TMessageHandler>(this IContext context, int mask) where TMessageDispatcher : IMessageDispatcher where TMessageHandler : IMessageHandler
        {
            var handlers = context.Resolve<HandlerCollector>().Collect<TMessageHandler>(mask);
            var dispatcher = context.Resolve<TMessageDispatcher>();
            foreach (var (instance, attribute) in handlers)
                dispatcher.Register(attribute.Id, instance);
        }
    }
}