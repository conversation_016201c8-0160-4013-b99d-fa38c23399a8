using System;
using System.IO;
using System.Xml;
using NLog;
using NLog.Config;
using slf4net;
using slf4net.NLog;
using ILogger = slf4net.ILogger;

namespace Ock
{
    public static class LoggerManager
    {
        static LoggerManager()
        {
            LoggerFactory.SetServiceProviderResolver(new ManualFactoryResolver(new NLogLoggerFactory()));
        }

        public static void LoadCfg(string text)
        {
            using var reader = XmlReader.Create(new StringReader(text));
            LogManager.Configuration = new XmlLoggingConfiguration(reader);
        }

        public static ILogger GetLogger(string name)
            => LoggerFactory.GetLogger(name);

        public static ILogger GetLogger(Type type)
            => LoggerFactory.GetLogger(type);
    }
}