﻿using UnityEngine;
using UnityEngine.Tilemaps;

namespace CLMM
{
    public static class World
    {
        public static readonly Vector2Int TileSize = new(3, 2);
        public const int ChunkSize = 32;
        public const int ChunkHalfSize = ChunkSize / 2;
        public const int ChunkCacheDistance = 1;
        public static readonly int TileLayer = LayerMask.NameToLayer("Tilemap");

        public static Grid Grid { get; set; }
        public static GameObject SpritePrefab { get; set; }
        public static Tilemap Tilemap { get; set; }
        public static Tilemap[] Tilemaps { get; } = new Tilemap[2];
        public static Transform ObjectTransform { get; set; }
        public static Map Map { get; set; }
        public static PlayerController PlayerController { get; set; }

        public static Camera Camera { get; set; }

        public static void Update()
        {
            if (Map != null)
                Map.Update();
        }
    }
}