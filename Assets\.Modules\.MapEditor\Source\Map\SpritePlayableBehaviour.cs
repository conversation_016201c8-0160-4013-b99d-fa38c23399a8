﻿using UnityEngine;
using UnityEngine.Playables;

namespace CLMM
{
    public class SpritePlayableBehaviour : PlayableBehaviour
    {
        public SpriteRenderer SpriteRenderer { get; set; }
        public SpriteAnimationClip Clip { get; set; }

        private Transform _transform;
        private double _frameInterval;
        private double _currentTime;
        private double _nextTime;
        private int _frameIndex;

        public override void OnBehaviourPlay(Playable playable, FrameData info)
        {
            _transform = SpriteRenderer.transform;
            _frameInterval = 1.0 / Clip.FrameRate;
            _currentTime = 0;
            _nextTime = _frameInterval;
            _frameIndex = 0;
        }

        public override void ProcessFrame(Playable playable, FrameData info, object playerData)
        {
            _currentTime += info.deltaTime;
            if (_currentTime < _nextTime)
                return;

            _nextTime += _frameInterval;
            _frameIndex++;

            if (_frameIndex >= Clip.Sprites.Length)
                _frameIndex = 0;

            SpriteRenderer.sprite = Clip.Sprites[_frameIndex];
            var point = _transform.localPosition;
            _transform.localPosition = point + new Vector3(0, 2 / 6f, 0);
        }
    }
}