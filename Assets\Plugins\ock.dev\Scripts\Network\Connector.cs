﻿using System.Net;
using System.Threading.Tasks;
using Autofac.Annotation;
using DotNetty.Transport.Bootstrapping;
using DotNetty.Transport.Channels;

namespace Ock
{
    [Component(typeof(IConnector), AutofacScope = AutofacScope.InstancePerLifetimeScope)]
    public class Connector : IConnector
    {
        private readonly Bootstrap _bootstrap = new();

        public Connector(IContext context)
        {
            _bootstrap
                .Group(((NetworkService)context.Resolve<IService>()).EventLoopGroup)
                .Channel<TcpSession>()
                .Option(ChannelOption.TcpNodelay, true)
                .Handler(new ChannelInitializer(context));
        }

        public Task<ISession> ConnectAsync(EndPoint address)
        {
            return _bootstrap.ConnectAsync(address)
                .ContinueWith(task =>
                {
                    if (task.IsFaulted)
                        throw task.Exception!;
                    return (ISession)task.Result;
                });
        }
    }
}