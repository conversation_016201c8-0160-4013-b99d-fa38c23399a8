using System;
using DotNetty.Buffers;
using DotNetty.Codecs;
using DotNetty.Transport.Channels;
using slf4net;

namespace Universe
{
    public class ProtocolEncoder : MessageToByteEncoder<IMessage>
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(ProtocolEncoder));

        private readonly IProtocolSerializer _serializer;

        public ProtocolEncoder(IProtocolSerializer serializer)
        {
            _serializer = serializer;
        }

        protected override void Encode(
            IChannelHandlerContext context,
            IMessage message,
            IByteBuffer output)
        {
            var offset = output.WriterIndex;
            output.SetWriterIndex(offset + sizeof(ushort));
            try
            {
                _serializer.Serialize(output, message);
            }
            catch (Exception e)
            {
                if (Logger.IsErrorEnabled)
                    Logger.Error($"{nameof(Encode)}: Error serialize packet.\r\nException - {e}");
            }

            output.SetUnsignedShortLE(offset, (ushort)(output.WriterIndex - offset));
        }
    }
}