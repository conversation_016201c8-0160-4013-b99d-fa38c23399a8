﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class MentorUpdateNotify : IMessage
    {
        public const int Opcode = 245;

        public int Id => Opcode;
    }

    [Packable(Id = MentorUpdateNotify.Opcode)]
    public class MentorUpdateNotifyPackable : MessagePackable<MentorUpdateNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out MentorUpdateNotify message)
        {
            message = new MentorUpdateNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}