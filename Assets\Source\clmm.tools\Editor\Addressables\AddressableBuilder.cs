using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEditor.AddressableAssets;
using UnityEditor.AddressableAssets.Settings;
using UnityEngine;

public static class AddressableBuilder
{
    [MenuItem("Tools/AddressableBuilder.Build")]
    public static void Build()
    {
        var settings = AddressableAssetSettingsDefaultObject.Settings;
        if (settings == null)
        {
            Debug.LogError("Addressable settings not found. Go to Window > Asset Management > Addressables > Groups");
            return;
        }

        var group = settings.DefaultGroup;
        if (group == null)
        {
            group = settings.CreateGroup("Default Local Group", false, false, true, null);
            settings.DefaultGroup = group;
        }

        Clean(settings);
        BuildUI(settings);
        BuildMap(settings);

        AssetDatabase.Refresh();
    }

    private static void Clean(AddressableAssetSettings settings)
    {
        var entries = new List<AddressableAssetEntry>();
        settings.GetAllAssets(entries, true);
        foreach (var entry in entries)
            settings.RemoveAssetEntry(entry.guid);
        EditorUtility.SetDirty(settings);
        AssetDatabase.SaveAssets();
    }

    private static void BuildMap(AddressableAssetSettings settings)
    {
        var directories = Directory.GetDirectories("Assets/Content/Map/Data");
        foreach (var directory in directories)
        {
            var folder = directory.Replace("\\", "/");
            var guid = AssetDatabase.AssetPathToGUID(folder);

            var entry = settings.CreateOrMoveEntry(guid, settings.DefaultGroup);
            entry.address = folder.Replace("Assets/Content/", "");
        }

        var guids = AssetDatabase.FindAssets("t:GameObject", new[] { "Assets/Content/Map/Prefab" });
        foreach (var guid in guids)
        {
            var assetPath = AssetDatabase.GUIDToAssetPath(guid);
            var entry = settings.CreateOrMoveEntry(guid, settings.DefaultGroup);
            entry.address = assetPath.Replace("Assets/Content/", "")
                .Replace(Path.GetExtension(assetPath), "");
        }

        BuildMapAtlas(settings);

        EditorUtility.SetDirty(settings);
        AssetDatabase.SaveAssets();
    }

    private static void BuildMapAtlas(AddressableAssetSettings settings)
    {
        var group = settings.groups.Find(x => x.name == "Map Atlas Group");

        // var guids = AssetDatabase.FindAssets("t:RefSpriteAtlas", new[] { "Assets/Content/Map" });
        var guids = AssetDatabase.FindAssets("t:TileAtlas", new[] { "Assets/Content/Map" });
        foreach (var guid in guids)
        {
            var assetPath = AssetDatabase.GUIDToAssetPath(guid);
            var entry = settings.CreateOrMoveEntry(guid, group);
            entry.address = assetPath.Replace("Assets/Content/", "")
                .Replace(Path.GetExtension(assetPath), "");
        }
    }

    private static void BuildUI(AddressableAssetSettings settings)
    {
        var group = settings.groups.Find(x => x.name == "UI Group");

        var guids = AssetDatabase.FindAssets("t:SpriteAtlas", new[] { "Assets/Content/UI/Atlas" });
        foreach (var guid in guids)
        {
            var assetPath = AssetDatabase.GUIDToAssetPath(guid);
            var entry = settings.CreateOrMoveEntry(guid, group);
            entry.address = assetPath.Replace("Assets/Content/", "")
                .Replace(Path.GetExtension(assetPath), "");
        }

        guids = AssetDatabase.FindAssets("t:GameObject", new[] { "Assets/Content/UI/Prefab" });
        foreach (var guid in guids)
        {
            var assetPath = AssetDatabase.GUIDToAssetPath(guid);
            var entry = settings.CreateOrMoveEntry(guid, group);
            entry.address = assetPath.Replace("Assets/Content/", "")
                .Replace(Path.GetExtension(assetPath), "");
        }

        EditorUtility.SetDirty(settings);
        AssetDatabase.SaveAssets();
    }
}