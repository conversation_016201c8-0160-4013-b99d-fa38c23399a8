﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class PauseBuffNotify : IMessage
    {
        public const int Opcode = 144;

        public int Id => Opcode;
    }

    [Packable(Id = PauseBuffNotify.Opcode)]
    public class PauseBuffNotifyPackable : MessagePackable<PauseBuffNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out PauseBuffNotify message)
        {
            message = new PauseBuffNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}