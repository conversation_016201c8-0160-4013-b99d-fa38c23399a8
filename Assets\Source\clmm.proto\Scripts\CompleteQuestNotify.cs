﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class CompleteQuestNotify : IMessage
    {
        public const int Opcode = 200;

        public int Id => Opcode;
    }

    [Packable(Id = CompleteQuestNotify.Opcode)]
    public class CompleteQuestNotifyPackable : MessagePackable<CompleteQuestNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out CompleteQuestNotify message)
        {
            message = new CompleteQuestNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}