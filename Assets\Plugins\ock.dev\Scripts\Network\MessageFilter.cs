using System;
using System.Collections.Generic;
using Autofac.Annotation;

namespace Ock
{
    [Component(typeof(IMessageFilter), AutofacScope = AutofacScope.InstancePerLifetimeScope)]
    public class MessageFilter : IMessageFilter
    {
        private readonly Dictionary<Type, LogLevel> _logLevelMappings = new();

        public LogLevel GetLogLevel(Type type, LogLevel @default = LogLevel.INFO)
        {
            return _logLevelMappings.TryGetValue(type, out var logLevel) ? logLevel : @default;
        }

        public void Add(Type messageType, LogLevel logLevel)
        {
            _logLevelMappings[messageType] = logLevel;
        }

        public bool Remove(Type messageType)
        {
            return _logLevelMappings.Remove(messageType);
        }

        public void AddRule(Type messageType, LogLevel filterLevel)
        {
            _logLevelMappings[messageType] = filterLevel;
        }

        public bool RemoveRule(Type messageType)
        {
            return _logLevelMappings.Remove(messageType);
        }

        public void RemoveAllRules()
        {
            _logLevelMappings.Clear();
        }

        public bool ShouldFilter(Type messageType, LogLevel filterLevel)
        {
            if (!_logLevelMappings.TryGetValue(messageType, out var logLevel))
                return false;

            return logLevel > filterLevel;
        }
    }
}