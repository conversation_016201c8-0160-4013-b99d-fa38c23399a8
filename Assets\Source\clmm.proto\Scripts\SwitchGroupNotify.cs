﻿using DotNetty.Buffers;
using Ock;

namespace CLMM.Proto
{
    public class SwitchGroupNotify : IMessage
    {
        public const int Opcode = 129;

        public int Id => Opcode;
    }

    [Packable(Id = SwitchGroupNotify.Opcode)]
    public class SwitchGroupNotifyPackable : MessagePackable<SwitchGroupNotify>
    {
        protected override void Deserialize(IByteBuffer buf, out SwitchGroupNotify message)
        {
            message = new SwitchGroupNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}