using Autofac.Annotation;
using Newtonsoft.Json;
using Ock;
using slf4net;

namespace CLMM.Domain
{
    [Component(typeof(IEventHandler), AutofacScope = AutofacScope.InstancePerLifetimeScope)]
    [Handler(GameStateChangedEvent.Opcode, Mask = (int)GameState.ANY)]
    public class GameStateChangedHandler : EventHandler<GameStateChangedEvent>
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(GameStateChangedHandler));

        protected override void Execute(GameStateChangedEvent @event)
        {
            if (Logger.IsInfoEnabled)
                Logger.Info($"{nameof(Execute)}: -I {nameof(GameStateChangedEvent)} {JsonConvert.SerializeObject(@event)}");
        }
    }
}