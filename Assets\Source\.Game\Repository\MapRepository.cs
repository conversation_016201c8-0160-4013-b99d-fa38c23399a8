// using CLMM.Proto;
// using R3;
// using slf4net;
// using Universe;
// using ILogger = slf4net.ILogger;
//
// namespace CLMM.Repository
// {
//     /// <summary>
//     /// 地图信息管理仓库
//     /// </summary>
//     [Component(Scope = Scope.InstancePerLifetimeScope)]
//     public class MapRepository : IRepository
//     {
//         private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(MapRepository));
//
//         /// <summary>
//         /// 当前地图信息
//         /// </summary>
//         // public ReactiveProperty<MapInformationNotify> CurrentMapInfo { get; } = new();
//
//         /// <summary>
//         /// 地图加载状态
//         /// </summary>
//         public ReactiveProperty<bool> IsMapLoaded { get; } = new();
//
//         public void Dispose()
//         {
//             // CurrentMapInfo?.Dispose();
//             IsMapLoaded?.Dispose();
//         }
//
//         /// <summary>
//         /// 更新地图信息
//         /// </summary>
//         /// <param name="mapInfo">地图信息</param>
//         // public void UpdateMapInformation(MapInformationNotify mapInfo)
//         // {
//         //     CurrentMapInfo.Value = mapInfo;
//         //     IsMapLoaded.Value = true;
//         // }
//
//         /// <summary>
//         /// 清除地图信息
//         /// </summary>
//         public void ClearMapInformation()
//         {
//             if (Logger.IsTraceEnabled)
//                 Logger.Trace("清除地图信息");
//
//             CurrentMapInfo.Value = null;
//             IsMapLoaded.Value = false;
//         }
//
//         /// <summary>
//         /// 获取当前地图索引
//         /// </summary>
//         /// <returns>地图索引，如果没有地图则返回-1</returns>
//         public int GetCurrentMapIndex()
//         {
//             return CurrentMapInfo.Value?.MapIndex ?? -1;
//         }
//
//         /// <summary>
//         /// 获取当前地图标题
//         /// </summary>
//         /// <returns>地图标题，如果没有地图则返回空字符串</returns>
//         public string GetCurrentMapTitle()
//         {
//             return CurrentMapInfo.Value?.Title ?? string.Empty;
//         }
//     }
// }

